# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build production version
- `npm run build:dev` - Build development version
- `npm run lint` - Run ESLint to check code quality
- `npm run preview` - Preview built application locally

## Project Architecture

This is a React invoice/receipt generator application built with Vite, using Brazilian Portuguese localization.

### Key Application Structure

- **Main App Flow**: `src/main.jsx` → `src/App.jsx` → Pages via React Router
- **Pages**: 
  - `src/pages/Index.jsx` - Main form for invoice data entry and template selection
  - `src/pages/TemplatePage.jsx` - Invoice template preview and PDF generation
  - `src/pages/ReceiptPage.jsx` - Receipt template preview and PDF generation
- **Templates**: Invoice templates in `src/components/templates/Template{1-9}.jsx`
- **UI Components**: shadcn/ui components in `src/components/ui/`

### State Management

- Uses React state with localStorage persistence
- Form data is automatically saved/loaded from localStorage in `src/pages/Index.jsx:86-138`
- Data flows between pages via React Router state

### Localization

- All UI text in Brazilian Portuguese via `src/utils/locale.js`
- Date formatting follows Brazilian standards (dd/MM/yyyy)
- Currency formatting supports BRL, USD, and INR via `src/utils/formatCurrency.js`

### PDF Generation

- Uses `html2canvas` + `jsPDF` for PDF export
- Two generators: `src/utils/pdfGenerator.js` (invoices) and `src/utils/receiptPDFGenerator.js` (receipts)
- Template selection handled by `src/utils/templateRegistry.js`

### Key Utilities

- `src/utils/invoiceCalculations.js` - Tax and total calculations
- `src/utils/messages-pt-br.js` - Brazilian Portuguese note templates
- `src/lib/utils.js` - Generic utility functions (likely shadcn/ui utils)

### Styling

- Tailwind CSS with custom theme configuration
- CSS variables for color system in `src/index.css`
- Dark mode support enabled via `tailwind.config.js`