import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Upload, Edit3 } from 'lucide-react';
import { translations } from '../utils/locale';
import InvoiceDetailsTab from '../components/v2/InvoiceDetailsTab';
import BusinessClientTab from '../components/v2/BusinessClientTab';
import ShippingTransportTab from '../components/v2/ShippingTransportTab';
import ItemsTab from '../components/v2/ItemsTab';
import PreviewSummaryTab from '../components/v2/PreviewSummaryTab';

const generateRandomInvoiceNumber = () => {
  const length = Math.floor(Math.random() * 6) + 3;
  const alphabetCount = Math.min(Math.floor(Math.random() * 4), length);
  let result = "";
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";

  for (let i = 0; i < alphabetCount; i++) {
    result += alphabet[Math.floor(Math.random() * alphabet.length)];
  }

  for (let i = alphabetCount; i < length; i++) {
    result += numbers[Math.floor(Math.random() * numbers.length)];
  }

  return result;
};

const IndexV2 = () => {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState("invoice-details");
  const [selectedCurrency, setSelectedCurrency] = useState("BRL");
  const [selectedTemplate, setSelectedTemplate] = useState(1);
  
  // Form data states
  const [invoiceData, setInvoiceData] = useState({
    number: "",
    date: "",
    paymentDate: "",
    logoUrl: "",
  });
  
  const [businessData, setBusinessData] = useState({
    name: "",
    address: "",
    phone: "",
    email: "",
    vatNumber: "",
    displayName: "",
    country: "Brazil",
    city: "",
    postalCode: "",
  });
  
  const [clientData, setClientData] = useState({
    name: "",
    address: "",
    phone: "",
    email: "",
    industry: "",
    country: "Brazil",
    city: "",
    postalCode: "",
  });
  
  const [shippingData, setShippingData] = useState({
    from: {
      warehouse: "",
      businessName: "",
      country: "",
      city: "",
      postalCode: "",
      address: "",
    },
    to: {
      address: "",
      businessName: "",
      country: "",
      city: "",
      postalCode: "",
    },
    transport: {
      details: "",
      transporter: "",
      distance: "",
      docNumber: "",
      docDate: "",
      vehicleType: "",
      vehicleNumber: "",
    }
  });
  
  const [items, setItems] = useState([
    { name: "", description: "", quantity: 0, rate: 0, amount: 0, tax: 0 }
  ]);
  
  const [taxPercentage, setTaxPercentage] = useState(0);
  const [notes, setNotes] = useState("");
  const [termsConditions, setTermsConditions] = useState([
    { text: "Please pay within 15 days from the date of invoice, overdue interest @ 14% will be charged on delayed payments." },
    { text: "Please quote invoice number when remitting funds." }
  ]);
  const [signature, setSignature] = useState("");
  const [pixData, setPixData] = useState({
    chavePix: "",
    nomeRecebedor: "",
    cidadeRecebedor: "",
  });

  // Load data from localStorage
  useEffect(() => {
    const savedFormData = localStorage.getItem("formDataV2");
    if (savedFormData) {
      const parsedData = JSON.parse(savedFormData);
      setInvoiceData(parsedData.invoiceData || { number: generateRandomInvoiceNumber(), date: "", paymentDate: "", logoUrl: "" });
      setBusinessData(parsedData.businessData || businessData);
      setClientData(parsedData.clientData || clientData);
      setShippingData(parsedData.shippingData || shippingData);
      setItems(parsedData.items || items);
      setTaxPercentage(parsedData.taxPercentage || 0);
      setNotes(parsedData.notes || "");
      setTermsConditions(parsedData.termsConditions || termsConditions);
      setSignature(parsedData.signature || "");
      setPixData(parsedData.pixData || pixData);
      setSelectedCurrency(parsedData.selectedCurrency || "BRL");
      setSelectedTemplate(parsedData.selectedTemplate || 1);
    } else {
      setInvoiceData(prev => ({ ...prev, number: generateRandomInvoiceNumber() }));
    }
  }, []);

  // Save data to localStorage
  useEffect(() => {
    const formData = {
      invoiceData,
      businessData,
      clientData,
      shippingData,
      items,
      taxPercentage,
      notes,
      termsConditions,
      signature,
      pixData,
      selectedCurrency,
      selectedTemplate,
    };
    localStorage.setItem("formDataV2", JSON.stringify(formData));
  }, [invoiceData, businessData, clientData, shippingData, items, taxPercentage, notes, termsConditions, signature, pixData, selectedCurrency, selectedTemplate]);

  const tabsConfig = [
    { id: "invoice-details", label: "Invoice Details", icon: Edit3 },
    { id: "business-client", label: "From/For", icon: Edit3 },
    { id: "shipping", label: "Shipping", icon: Edit3 },
    { id: "items", label: "Items", icon: Edit3 },
    { id: "preview", label: "Preview", icon: Edit3 },
  ];

  const getCurrentTabIndex = () => {
    return tabsConfig.findIndex(tab => tab.id === currentTab);
  };

  const goToNextTab = () => {
    const currentIndex = getCurrentTabIndex();
    if (currentIndex < tabsConfig.length - 1) {
      setCurrentTab(tabsConfig[currentIndex + 1].id);
    }
  };

  const goToPreviousTab = () => {
    const currentIndex = getCurrentTabIndex();
    if (currentIndex > 0) {
      setCurrentTab(tabsConfig[currentIndex - 1].id);
    }
  };

  const handleTemplateSelection = (templateNumber) => {
    setSelectedTemplate(templateNumber);
  };

  const handleFullPreview = () => {
    const formData = {
      billTo: { name: clientData.name, address: clientData.address, phone: clientData.phone },
      shipTo: { name: clientData.name, address: clientData.address, phone: clientData.phone },
      invoice: { date: invoiceData.date, paymentDate: invoiceData.paymentDate, number: invoiceData.number },
      yourCompany: { name: businessData.name, address: businessData.address, phone: businessData.phone },
      items: items.map(item => ({ ...item, amount: item.rate, total: item.amount })),
      taxPercentage,
      taxAmount: items.reduce((sum, item) => sum + (item.amount * taxPercentage / 100), 0),
      subTotal: items.reduce((sum, item) => sum + item.amount, 0),
      grandTotal: items.reduce((sum, item) => sum + item.amount, 0) + items.reduce((sum, item) => sum + (item.amount * taxPercentage / 100), 0),
      notes,
      selectedCurrency,
      pixData,
    };
    
    navigate("/template", {
      state: { formData, selectedTemplate: selectedTemplate },
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">Invoice</h1>
              <Button variant="ghost" size="sm" className="text-purple-600">
                <Upload className="w-4 h-4 mr-1" />
                Add Business Logo
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" onClick={() => navigate("/")}>
                Versão Antiga
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <Card className="w-full max-w-6xl mx-auto">
          <CardContent className="p-0">
            <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
              <div className="border-b bg-gray-50/50">
                <TabsList className="w-full justify-start h-auto p-0 bg-transparent">
                  {tabsConfig.map((tab) => (
                    <TabsTrigger
                      key={tab.id}
                      value={tab.id}
                      className="flex-1 data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-purple-600 py-4 px-6 font-medium"
                    >
                      <tab.icon className="w-4 h-4 mr-2" />
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>

              <div className="p-8">
                <TabsContent value="invoice-details" className="mt-0">
                  <InvoiceDetailsTab
                    invoiceData={invoiceData}
                    setInvoiceData={setInvoiceData}
                  />
                </TabsContent>

                <TabsContent value="business-client" className="mt-0">
                  <BusinessClientTab
                    businessData={businessData}
                    setBusinessData={setBusinessData}
                    clientData={clientData}
                    setClientData={setClientData}
                  />
                </TabsContent>

                <TabsContent value="shipping" className="mt-0">
                  <ShippingTransportTab
                    shippingData={shippingData}
                    setShippingData={setShippingData}
                  />
                </TabsContent>

                <TabsContent value="items" className="mt-0">
                  <ItemsTab
                    items={items}
                    setItems={setItems}
                    selectedCurrency={selectedCurrency}
                    setSelectedCurrency={setSelectedCurrency}
                    taxPercentage={taxPercentage}
                    setTaxPercentage={setTaxPercentage}
                  />
                </TabsContent>

                <TabsContent value="preview" className="mt-0">
                  <PreviewSummaryTab
                    invoiceData={invoiceData}
                    businessData={businessData}
                    clientData={clientData}
                    items={items}
                    taxPercentage={taxPercentage}
                    selectedCurrency={selectedCurrency}
                    selectedTemplate={selectedTemplate}
                    notes={notes}
                    setNotes={setNotes}
                    termsConditions={termsConditions}
                    setTermsConditions={setTermsConditions}
                    signature={signature}
                    setSignature={setSignature}
                    pixData={pixData}
                    setPixData={setPixData}
                    onTemplateSelect={handleTemplateSelection}
                    onFullPreview={handleFullPreview}
                  />
                </TabsContent>
              </div>

              {/* Footer Navigation */}
              <div className="flex justify-between items-center p-6 border-t bg-gray-50">
                <Button
                  variant="outline"
                  onClick={goToPreviousTab}
                  disabled={getCurrentTabIndex() === 0}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>
                
                <div className="flex space-x-2">
                  {tabsConfig.map((_, index) => (
                    <div
                      key={index}
                      className={`w-3 h-3 rounded-full ${
                        index === getCurrentTabIndex()
                          ? "bg-purple-600"
                          : index < getCurrentTabIndex()
                          ? "bg-green-500"
                          : "bg-gray-300"
                      }`}
                    />
                  ))}
                </div>

                <Button
                  onClick={goToNextTab}
                  disabled={getCurrentTabIndex() === tabsConfig.length - 1}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default IndexV2;