import QRCode from 'qrcode';

/**
 * Gera a string EMV para código PIX
 * @param {Object} pixData - Dados do PIX
 * @param {string} pixData.chavePix - Chave PIX (CPF, CNPJ, telefone, email ou chave aleatória)
 * @param {string} pixData.nomeRecebedor - Nome do recebedor
 * @param {string} pixData.cidadeRecebedor - Cidade do recebedor
 * @param {number} pixData.valor - Valor da transação
 * @param {string} [pixData.descricao] - Descrição opcional da transação
 * @param {string} [pixData.identificador] - Identificador da transação
 * @returns {string} String EMV do PIX
 */
function generatePixEMV(pixData) {
  const {
    chavePix,
    nomeRecebedor,
    cidadeRecebedor,
    valor,
    descricao = '',
    identificador = ''
  } = pixData;

  // Função auxiliar para adicionar campo EMV
  const addEMVField = (id, value) => {
    const length = value.length.toString().padStart(2, '0');
    return `${id}${length}${value}`;
  };

  // Indicador de formato PIX
  let emv = addEMVField('00', '01');
  
  // Se tem valor, indica PIX dinâmico, senão estático
  if (valor > 0) {
    emv += addEMVField('01', '12'); // PIX dinâmico
  } else {
    emv += addEMVField('01', '11'); // PIX estático
  }

  // Dados da conta de recebimento PIX (26)
  let merchantAccountInfo = '';
  merchantAccountInfo += addEMVField('00', 'br.gov.bcb.pix'); // GUI
  merchantAccountInfo += addEMVField('01', chavePix); // Chave PIX
  
  if (descricao) {
    merchantAccountInfo += addEMVField('02', descricao);
  }

  emv += addEMVField('26', merchantAccountInfo);

  // Código da moeda (986 = BRL)
  emv += addEMVField('53', '986');

  // Valor da transação (se houver)
  if (valor > 0) {
    emv += addEMVField('54', valor.toFixed(2));
  }

  // País
  emv += addEMVField('58', 'BR');

  // Nome do recebedor
  emv += addEMVField('59', nomeRecebedor.substring(0, 25));

  // Cidade do recebedor
  emv += addEMVField('60', cidadeRecebedor.substring(0, 15));

  // Informações adicionais (se houver identificador)
  if (identificador) {
    const additionalInfo = addEMVField('05', identificador.substring(0, 25));
    emv += addEMVField('62', additionalInfo);
  }

  // Calcular CRC16
  emv += '6304';
  const crc = calculateCRC16(emv).toString(16).toUpperCase().padStart(4, '0');
  emv = emv.substring(0, emv.length - 4) + '63' + '04' + crc;

  return emv;
}

/**
 * Calcula o CRC16 para validação PIX
 */
function calculateCRC16(str) {
  const crcTable = [];
  for (let i = 0; i < 256; i++) {
    let crc = i << 8;
    for (let j = 0; j < 8; j++) {
      crc = (crc & 0x8000) ? ((crc << 1) ^ 0x1021) : (crc << 1);
    }
    crcTable[i] = crc & 0xFFFF;
  }

  let crc = 0xFFFF;
  for (let i = 0; i < str.length; i++) {
    const byte = str.charCodeAt(i);
    crc = ((crc << 8) ^ crcTable[((crc >> 8) ^ byte) & 0xFF]) & 0xFFFF;
  }

  return crc;
}

/**
 * Gera um QR Code PIX
 * @param {Object} pixData - Dados do PIX
 * @param {Object} options - Opções do QR Code
 * @param {number} options.width - Largura do QR Code
 * @param {string} options.color.dark - Cor escura
 * @param {string} options.color.light - Cor clara
 * @returns {Promise<string>} Data URL do QR Code
 */
export async function generatePixQRCode(pixData, options = {}) {
  const defaultOptions = {
    width: 200,
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    },
    errorCorrectionLevel: 'M'
  };

  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    const emvString = generatePixEMV(pixData);
    const qrCodeDataURL = await QRCode.toDataURL(emvString, finalOptions);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Erro ao gerar QR Code PIX:', error);
    throw new Error('Falha ao gerar QR Code PIX');
  }
}

/**
 * Valida os dados necessários para gerar um PIX
 * @param {Object} pixData - Dados do PIX
 * @returns {boolean} Se os dados são válidos
 */
export function validatePixData(pixData) {
  const { chavePix, nomeRecebedor, cidadeRecebedor } = pixData;
  
  if (!chavePix || !nomeRecebedor || !cidadeRecebedor) {
    return false;
  }

  // Validação básica da chave PIX (pode ser melhorada)
  if (chavePix.length < 4) {
    return false;
  }

  return true;
}

export { generatePixEMV };