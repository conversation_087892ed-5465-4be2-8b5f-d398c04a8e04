export const formatCurrency = (amount, currencyCode = 'BRL', minimumFractionDigits = 2) => {
  // Usar locale pt-BR para formatação brasileira
  const locale = 'pt-BR';
  
  // Mapear códigos de moeda para o formato correto
  const currencyMap = {
    'BRL': 'BRL',
    'USD': 'USD',
    'INR': 'INR'
  };
  
  const currency = currencyMap[currencyCode] || 'BRL';
  
  return new Intl.NumberFormat(locale, { 
    style: 'currency', 
    currency: currency, 
    minimumFractionDigits 
  }).format(amount);
};

export const getCurrencySymbol = (currencyCode) => {
  return formatCurrency(0, currencyCode).replace(/[\d.,\s]/g, '');
};
