// Configurações de localização para pt-BR
import { ptBR } from 'date-fns/locale';

export const locale = ptBR;

// Textos da interface em português brasileiro
export const translations = {
  // Páginas principais
  billGenerator: 'Gerador de Faturas',
  receiptGenerator: 'Gerador de Recibos',
  invoiceInformation: 'Informações da Fatura',
  
  // Formulários
  billTo: 'Faturar Para',
  shipTo: 'Enviar Para',
  yourCompany: 'Sua Empresa',
  itemDetails: 'Detalhes dos Itens',
  
  // Campos
  name: 'Nome',
  address: 'Endereço',
  phone: 'Telefone',
  invoiceNumber: 'Número da Fatura',
  invoiceDate: 'Data da Fatura',
  paymentDate: 'Data de Vencimento',
  dueDate: 'Data de Vencimento',
  quantity: 'Quantidade',
  amount: 'Valor',
  total: 'Total',
  description: 'Descrição',
  item: 'Item',
  unitPrice: 'Preço Unitário',
  
  // Totais
  subtotal: 'Subtotal',
  taxRate: 'Taxa de Imposto (%)',
  taxAmount: 'Valor do Imposto',
  grandTotal: 'Total Geral',
  
  // Botões
  addItem: 'Adicionar Item',
  downloadPDF: 'Baixar PDF',
  downloading: 'Baixando...',
  back: 'Voltar',
  clearForm: 'Limpar Formulário',
  fillWithDummyData: 'Preencher com Dados de Exemplo',
  switchToReceipt: 'Alternar para Recibo',
  switchToBillGenerator: 'Alternar para Gerador de Faturas',
  downloadReceiptPDF: 'Baixar PDF do Recibo',
  
  // Outros
  notes: 'Observações',
  footer: 'Rodapé',
  cashier: 'Operador de Caixa',
  gstNo: 'Nº GST',
  receiptType: 'Tipo de Recibo',
  receiptPreview: 'Visualizar Recibo',
  selectCurrency: 'Selecionar Moeda',
  
  // Moedas
  currencies: {
    BRL: 'Real (R$)',
    USD: 'Dólar (US$)',
    INR: 'Rupia (₹)'
  },
  
  // Mensagens
  thankYouBusiness: 'Obrigado pelo seu negócio!',
  keepReceiptReturns: 'Guarde este recibo para devoluções ou trocas.',
  haveGreatDay: 'Tenha um ótimo dia!',
  loading: 'Carregando...',
  
  // Templates
  invoice: 'FATURA',
  receipt: 'RECIBO',
  
  // Validação
  fieldRequired: 'Este campo é obrigatório',
  invalidEmail: 'Email inválido',
  invalidPhone: 'Telefone inválido'
};

// Função para formatação de data brasileira
export const formatDateBR = (date, format = 'dd/MM/yyyy') => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Formatação manual para garantir formato brasileiro
  const day = dateObj.getDate().toString().padStart(2, '0');
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const year = dateObj.getFullYear();
  
  if (format === 'dd/MM/yyyy') {
    return `${day}/${month}/${year}`;
  }
  
  return `${day}/${month}/${year}`;
};

// Função para formatação de número brasileiro
export const formatNumberBR = (number, decimals = 2) => {
  if (number === null || number === undefined || isNaN(number)) return '0,00';
  
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number);
};

// Função para parsing de número brasileiro (vírgula para ponto)
export const parseNumberBR = (value) => {
  if (typeof value === 'number') return value;
  if (!value) return 0;
  
  // Remover pontos (separadores de milhares) e trocar vírgula por ponto
  const cleanValue = value.toString().replace(/\./g, '').replace(',', '.');
  return parseFloat(cleanValue) || 0;
};