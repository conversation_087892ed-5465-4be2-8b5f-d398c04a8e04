import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template10 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        {/* Header com design moderno */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-800 mb-2">INVOICE</h1>
            <div className="w-16 h-1 bg-blue-500 mb-4"></div>
            <div className="text-sm text-gray-600">
              <p className="font-semibold">{yourCompany.name || "Your Company Name"}</p>
              <p>{yourCompany.address || "Company Address"}</p>
              <p>{yourCompany.phone || "Phone Number"}</p>
              <p>{yourCompany.email || "<EMAIL>"}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="bg-blue-500 text-white px-4 py-2 rounded-lg mb-4">
              <span className="text-lg font-bold">#{invoice.number || "001"}</span>
            </div>
            <div className="text-sm text-gray-600">
              <p><span className="font-semibold">Data:</span> {formatDateBR(invoice.date) || "01/01/2024"}</p>
              <p><span className="font-semibold">Vencimento:</span> {formatDateBR(invoice.paymentDate) || "31/01/2024"}</p>
            </div>
          </div>
        </div>

        {/* Cliente */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1">FATURAR PARA</h3>
          <div className="text-sm text-gray-700">
            <p className="font-semibold text-base">{billTo.name || "Nome do Cliente"}</p>
            <p>{billTo.address || "Endereço do Cliente"}</p>
            <p>{billTo.phone || "Telefone do Cliente"}</p>
            <p>{billTo.email || "<EMAIL>"}</p>
          </div>
        </div>

        {/* Tabela de itens */}
        <div className="mb-8">
          <table className="w-full">
            <thead>
              <tr className="bg-blue-500 text-white">
                <th className="text-left py-3 px-4 font-semibold">DESCRIÇÃO</th>
                <th className="text-center py-3 px-4 font-semibold">QTD</th>
                <th className="text-right py-3 px-4 font-semibold">VALOR UNIT.</th>
                <th className="text-right py-3 px-4 font-semibold">TOTAL</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                  <td className="py-3 px-4 border-b border-gray-200">
                    <div className="font-medium">{item.name || `Item ${index + 1}`}</div>
                    {item.description && (
                      <div className="text-sm text-gray-600">{item.description}</div>
                    )}
                  </td>
                  <td className="py-3 px-4 text-center border-b border-gray-200">{item.quantity || 1}</td>
                  <td className="py-3 px-4 text-right border-b border-gray-200">
                    {formatCurrency(item.amount || 0, selectedCurrency)}
                  </td>
                  <td className="py-3 px-4 text-right border-b border-gray-200 font-medium">
                    {formatCurrency(item.total || 0, selectedCurrency)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totais */}
        <div className="flex justify-end mb-8">
          <div className="w-80">
            <div className="flex justify-between py-2 border-b border-gray-200">
              <span className="text-gray-700">Subtotal:</span>
              <span className="font-medium">{formatCurrency(subTotal, selectedCurrency)}</span>
            </div>
            {taxPercentage > 0 && (
              <div className="flex justify-between py-2 border-b border-gray-200">
                <span className="text-gray-700">Taxa ({taxPercentage}%):</span>
                <span className="font-medium">{formatCurrency(taxAmount, selectedCurrency)}</span>
              </div>
            )}
            <div className="flex justify-between py-3 border-t-2 border-blue-500 mt-2">
              <span className="text-lg font-bold text-gray-800">TOTAL:</span>
              <span className="text-lg font-bold text-blue-500">{formatCurrency(grandTotal, selectedCurrency)}</span>
            </div>
          </div>
        </div>

        {/* PIX QR Code */}
        {pixData && pixData.key && (
          <div className="mb-8 p-6 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Pagamento via PIX</h3>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm text-gray-600 mb-2">Chave PIX:</p>
                <p className="font-mono text-sm bg-white p-2 rounded border">{pixData.key}</p>
                {pixData.description && (
                  <p className="text-sm text-gray-600 mt-2">{pixData.description}</p>
                )}
              </div>
              <div className="ml-6">
                <PixQRCode pixData={pixData} />
              </div>
            </div>
          </div>
        )}

        {/* Notas */}
        {notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1">OBSERVAÇÕES</h3>
            <p className="text-sm text-gray-700 leading-relaxed">{notes}</p>
          </div>
        )}

        {/* Footer */}
        <div className="border-t-2 border-blue-500 pt-6 text-center">
          <p className="text-sm text-gray-600">
            Obrigado pela preferência! • {yourCompany.name || "Sua Empresa"}
          </p>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template10;
