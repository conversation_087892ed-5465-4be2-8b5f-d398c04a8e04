import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template2 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white max-w-4xl mx-auto">
        {/* Header com design baseado na imagem template-2 */}
        <div className="bg-gray-100 p-8">
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-4xl font-normal text-green-600 mb-2">FATURA</h1>
              <div className="bg-red-500 text-white px-3 py-1 rounded inline-block">
                <span className="font-bold">Não Pago</span>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center justify-end mb-4">
                <div className="bg-green-600 text-white px-2 py-1 rounded mr-2">
                  <span className="font-bold">F</span>
                </div>
                <div className="text-right">
                  <h2 className="text-lg font-bold text-green-600">FOOBAR</h2>
                  <p className="text-lg font-bold text-green-600">LABS</p>
                </div>
              </div>
              <div className="text-sm text-right">
                <p className="font-semibold">Faturado por</p>
                <p className="font-bold">{yourCompany.name || "Foobar Labs"}</p>
                <p>{yourCompany.address || "46, Raghuveer Dham Society"}</p>
                <p>Surat, Gujarat, India - 394710</p>
              </div>
            </div>
          </div>
        </div>

        {/* Seções de informações */}
        <div className="p-8">
          <div className="grid grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="font-semibold text-gray-600 mb-2">Faturado para</h3>
              <p className="font-bold">{billTo.name || "Studio Den"}</p>
              <p className="text-sm">{billTo.address || "305, 3rd Floor Orion mall, Bengaluru"}</p>
              <p className="text-sm">Karnataka, India - 560055</p>
              <div className="mt-4 text-sm">
                <p><span className="font-semibold">CNPJ:</span> {billTo.cnpj || "29.123.456/0001-78"}</p>
                <p><span className="font-semibold">IE:</span> {billTo.ie || "*********"}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-600 mb-2">Detalhes da Fatura</h3>
              <div className="text-sm space-y-1">
                <p><span className="font-semibold">Fatura #:</span> {invoice.number || "003"}</p>
                <p><span className="font-semibold">Data da Fatura:</span> {invoice.date ? formatDateBR(invoice.date) : "19 FEV, 2020"}</p>
                <p><span className="font-semibold">Data de Vencimento:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "19 FEV, 2020"}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-600 mb-2">Registro de Pagamento</h3>
              <div className="text-sm space-y-1">
                <p><span className="font-semibold">Valor Pago:</span> {formatCurrency(0, selectedCurrency)}</p>
                <p><span className="font-semibold text-green-600 text-xl">Valor Devido:</span> <span className="text-green-600 text-xl font-bold">{formatCurrency(grandTotal, selectedCurrency)}</span></p>
              </div>
            </div>
          </div>

          {/* Tabela de itens */}
          <table className="w-full mb-8">
            <thead>
              <tr className="bg-green-600 text-white">
                <th className="p-3 text-left">Item/Descrição do Item</th>
                <th className="p-3 text-center">Código</th>
                <th className="p-3 text-center">Qtd.</th>
                <th className="p-3 text-center">Taxa</th>
                <th className="p-3 text-center">Valor Tributável</th>
                <th className="p-3 text-center">ICMS</th>
                <th className="p-3 text-center">IPI</th>
                <th className="p-3 text-right">Valor</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={index} className="border-b">
                  <td className="p-3">
                    <div className="font-medium">{item.name || "Desenvolvimento Web Básico"}</div>
                    {item.description && (
                      <div className="text-sm text-gray-600">{item.description}</div>
                    )}
                  </td>
                  <td className="p-3 text-center">02</td>
                  <td className="p-3 text-center">{item.quantity || 1}</td>
                  <td className="p-3 text-center">{taxPercentage}%</td>
                  <td className="p-3 text-center">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
                  <td className="p-3 text-center">{formatCurrency((item.amount || 0) * 0.09, selectedCurrency)}</td>
                  <td className="p-3 text-center">{formatCurrency((item.amount || 0) * 0.09, selectedCurrency)}</td>
                  <td className="p-3 text-right font-semibold">{formatCurrency(item.total || 0, selectedCurrency)}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Seção inferior */}
          <div className="grid grid-cols-2 gap-8">
            {/* Coluna esquerda */}
            <div>
              <div className="mb-6">
                <p><span className="font-semibold">País de fornecimento:</span> Brasil</p>
                <p><span className="font-semibold">Local de fornecimento:</span> São Paulo</p>
              </div>

              <div className="mb-6">
                <h4 className="font-semibold mb-2">Total da fatura por extenso</h4>
                <p className="text-green-600 font-semibold">Quarenta e dois mil quatrocentos e oitenta reais</p>
              </div>

              <div className="mb-6">
                <h4 className="font-semibold mb-2">Pagamentos</h4>
                <table className="w-full text-sm border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="p-2 text-left border">Data</th>
                      <th className="p-2 text-left border">Modo</th>
                      <th className="p-2 text-left border">Status</th>
                      <th className="p-2 text-right border">Valor</th>
                      <th className="p-2 text-right border">Taxa</th>
                      <th className="p-2 text-right border">Desconto</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-2 border">06 Mai, 2020</td>
                      <td className="p-2 border">PIX</td>
                      <td className="p-2 border"><span className="bg-green-500 text-white px-2 py-1 rounded text-xs">Aprovado</span></td>
                      <td className="p-2 text-right border">{formatCurrency(3000, selectedCurrency)}</td>
                      <td className="p-2 text-right border">{formatCurrency(0, selectedCurrency)}</td>
                      <td className="p-2 text-right border">{formatCurrency(0, selectedCurrency)}</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="p-2 border" colSpan="3"><span className="font-semibold">Total</span></td>
                      <td className="p-2 text-right border font-semibold">{formatCurrency(3000, selectedCurrency)}</td>
                      <td className="p-2 text-right border font-semibold">{formatCurrency(0, selectedCurrency)}</td>
                      <td className="p-2 text-right border font-semibold">{formatCurrency(0, selectedCurrency)}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Termos e Condições</h4>
                <p className="text-sm mb-2">1. Favor pagar em até 15 dias a partir da data da fatura. Juros de 14% serão cobrados sobre pagamentos em atraso.</p>
                <p className="text-sm">2. Favor citar o número da fatura ao efetuar o pagamento.</p>
              </div>

              <div className="mt-6">
                <h4 className="font-semibold mb-2">Observações Adicionais</h4>
                <p className="text-sm">{notes || "É um fato estabelecido que um leitor será distraído pelo conteúdo legível de uma página ao olhar seu layout. O ponto de usar Lorem Ipsum é que ele tem uma distribuição mais ou menos normal de letras."}</p>
              </div>
            </div>
            
            {/* Coluna direita */}
            <div>
              <div className="mb-6">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>{formatCurrency(subTotal, selectedCurrency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Desconto (10%)</span>
                    <span>- {formatCurrency(subTotal * 0.1, selectedCurrency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Valor Tributável</span>
                    <span>{formatCurrency(subTotal * 0.9, selectedCurrency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ICMS</span>
                    <span>{formatCurrency(taxAmount / 2, selectedCurrency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>IPI</span>
                    <span>{formatCurrency(taxAmount / 2, selectedCurrency)}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>Total Devido</span>
                    <span className="text-green-600">{formatCurrency(grandTotal, selectedCurrency)}</span>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="font-semibold mb-4">Detalhes Bancários e Pagamento</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p><span className="font-semibold">Nome do Titular:</span> {bankDetails.accountHolderName || "Foobar Labs"}</p>
                    <p><span className="font-semibold">Número da Conta:</span> {bankDetails.accountNumber || "************"}</p>
                    <p><span className="font-semibold">Agência:</span> {bankDetails.ifsc || "0018159"}</p>
                    <p><span className="font-semibold">Tipo de Conta:</span> {bankDetails.accountType || "Poupança"}</p>
                    <p><span className="font-semibold">Banco:</span> {bankDetails.bankName || "Banco do Brasil"}</p>
                    <p><span className="font-semibold">PIX:</span> {pixData?.key || "<EMAIL>"}</p>
                  </div>
                  <div className="flex justify-center">
                    <div className="text-center">
                      <p className="text-xs mb-2">PIX - Escaneie para Pagar</p>
                      <PixQRCode
                        pixData={pixData}
                        grandTotal={grandTotal}
                        size={120}
                        className="border"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template2;
