import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template7 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{translations.invoice}</h1>
            <div className="text-sm space-y-1">
              <p><span className="font-semibold">Invoice #:</span> {invoice.number || "004"}</p>
              <p><span className="font-semibold">Invoice Date:</span> {invoice.date ? formatDateBR(invoice.date) : "JUN 19, 2019"}</p>
              <p><span className="font-semibold">Due Date:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "JUN 28, 2019"}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center justify-end mb-4">
              <div className="bg-gray-800 text-white px-2 py-1 rounded mr-2">
                <span className="font-bold">F</span>
              </div>
              <div className="text-right">
                <h2 className="text-lg font-bold text-gray-800">FOOBAR</h2>
                <p className="text-lg font-bold text-gray-800">LABS</p>
              </div>
            </div>
          </div>
        </div>

        {/* Seções Billed by e Billed to */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-bold text-gray-800 mb-4">Billed by</h3>
            <p className="font-bold">Foobar Labs</p>
            <p className="text-sm">46, Raghuveer Dham Society</p>
            <p className="text-sm">Bengaluru, Karnataka, India - 560054</p>
            <div className="mt-4 text-sm space-y-1">
              <p><span className="font-semibold">GSTIN:</span> 29**********Z75</p>
              <p><span className="font-semibold">PAN:</span> **********</p>
              <p className="mt-2"><span className="font-semibold">Place of Supply:</span> Karnataka</p>
            </div>
          </div>
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-bold text-gray-800 mb-4">Billed to</h3>
            <p className="font-bold">{billTo.name || "Wox Studio"}</p>
            <p className="text-sm">{billTo.address || "305, 3rd Floor Orion mall, Bengaluru, Karnataka, India - 560055"}</p>
            <div className="mt-4 text-sm space-y-1">
              <p><span className="font-semibold">GSTIN:</span> {billTo.gst || "29**********Z26"}</p>
              <p><span className="font-semibold">PAN:</span> {billTo.pan || "**********"}</p>
              <p className="mt-2"><span className="font-semibold">Country of Supply:</span> India</p>
            </div>
          </div>
        </div>

        {/* Tabela de itens */}
        <table className="w-full mb-8 border border-gray-400">
          <thead className="bg-gray-800 text-white">
            <tr>
              <th className="p-3 text-left">Item #/Item description</th>
              <th className="p-3 text-center">HSN</th>
              <th className="p-3 text-center">Qty.</th>
              <th className="p-3 text-center">GST</th>
              <th className="p-3 text-center">Taxable Amount</th>
              <th className="p-3 text-center">SGST</th>
              <th className="p-3 text-center">CGST</th>
              <th className="p-3 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index} className="border-b">
                <td className="p-3">
                  <p className="font-semibold">{`${index + 1}. ${item.name || "Basic Web Development"}`}</p>
                </td>
                <td className="p-3 text-center">02</td>
                <td className="p-3 text-center">{item.quantity || 10}</td>
                <td className="p-3 text-center">{taxPercentage}%</td>
                <td className="p-3 text-center">{formatCurrency(item.amount || 10000, selectedCurrency)}</td>
                <td className="p-3 text-center">₹900</td>
                <td className="p-3 text-center">₹900</td>
                <td className="p-3 text-right font-semibold">{formatCurrency((item.quantity || 0) * (item.amount || 0), selectedCurrency)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Seção inferior */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          {/* Coluna esquerda - Bank & Payment Details */}
          <div>
            <h4 className="font-semibold mb-4">Bank & Payment Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><span className="font-semibold">Account Holder Name:</span> {bankDetails.accountHolderName || "Foobar Labs"}</p>
                <p><span className="font-semibold">Account Number:</span> {bankDetails.accountNumber || "************"}</p>
                <p><span className="font-semibold">IFSC:</span> {bankDetails.ifsc || "SBIN0018159"}</p>
                <p><span className="font-semibold">Account Type:</span> {bankDetails.accountType || "Savings"}</p>
                <p><span className="font-semibold">Bank:</span> {bankDetails.bankName || "State Bank of India"}</p>
                <p><span className="font-semibold">UPI:</span> {bankDetails.upi || "foobarlabs@okisbi"}</p>
              </div>
              <div className="flex justify-center">
                <div className="text-center">
                  <p className="text-xs mb-2">UPI - Scan to Pay</p>
                  <PixQRCode 
                    pixData={pixData}
                    grandTotal={grandTotal}
                    size={120}
                    className="border"
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <h4 className="font-semibold mb-2">Terms and Conditions</h4>
              <p className="text-sm mb-2">1. Please pay within 15 days from the date of invoice, overdue interest @ 14% will be charged on delayed payments.</p>
              <p className="text-sm">2. Please quote invoice number when remitting funds.</p>
            </div>
            
            <div className="mt-6">
              <h4 className="font-semibold mb-2">Additional Notes</h4>
              <p className="text-sm">{notes || "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here'."}</p>
            </div>
          </div>
          
          {/* Coluna direita - Totais */}
          <div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Sub Total</span>
                <span>{formatCurrency(subTotal, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>Discount(10%)</span>
                <span>- ₹4000</span>
              </div>
              <div className="flex justify-between">
                <span>Taxable Amount</span>
                <span>₹36,000</span>
              </div>
              <div className="flex justify-between">
                <span>CGST</span>
                <span>₹3,240</span>
              </div>
              <div className="flex justify-between">
                <span>SGST</span>
                <span>₹3,240</span>
              </div>
              <div className="flex justify-between font-bold text-xl border-t pt-2 mt-4">
                <span>Total</span>
                <span>{formatCurrency(grandTotal, selectedCurrency)}</span>
              </div>
            </div>
            
            <div className="mt-6 text-sm">
              <h4 className="font-semibold mb-2">Invoice Total in words</h4>
              <p className="font-semibold">Forty-Two Thousand Four Hundred And Eighty Rupees Only</p>
            </div>
            
            <div className="mt-6 text-sm">
              <h4 className="font-semibold mb-2">EarlyPay Discount</h4>
              <p className="text-red-600 font-bold">₹200</p>
              <p className="text-xs">If paid before Dec 19, 2019 | 09:00 PM</p>
              
              <h4 className="font-semibold mt-4 mb-2">EarlyPay Amount</h4>
              <p className="font-bold">{formatCurrency(grandTotal - 200, selectedCurrency)}</p>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template7;
