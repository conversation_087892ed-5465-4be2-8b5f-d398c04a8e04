import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template9 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        {/* Header com design baseado na imagem template-9 */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold text-pink-600 mb-2">FATURA</h1>
            <h2 className="text-xl font-bold text-gray-800">
              {yourCompany.name || "Nome da Sua Empresa"}
            </h2>
            <p className="text-gray-600">{yourCompany.address || "Endereço da Empresa"}</p>
            <p className="text-gray-600">{yourCompany.phone || "Telefone da Empresa"}</p>
          </div>
          <div className="text-right text-gray-600">
            <p>
              <span className="font-semibold">Número da Fatura:</span>{" "}
              {invoice.number || "009"}
            </p>
            <p>
              <span className="font-semibold">Data da Fatura:</span>{" "}
              {invoice.date
                ? formatDateBR(invoice.date)
                : "25 AGO, 2019"}
            </p>
            <p>
              <span className="font-semibold">Data de Vencimento:</span>{" "}
              {invoice.paymentDate
                ? formatDateBR(invoice.paymentDate)
                : "05 SET, 2019"}
            </p>
          </div>
        </div>

        {/* Seções de informações */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div className="bg-pink-50 p-6 rounded-lg border-l-4 border-pink-600">
            <h3 className="text-lg font-semibold text-pink-600 mb-3">
              Faturado por
            </h3>
            <p className="font-bold">{yourCompany.name || "Nome da Sua Empresa"}</p>
            <p className="text-gray-600">{yourCompany.address || "Endereço da Sua Empresa"}</p>
            <p className="text-gray-600">{yourCompany.phone || "Telefone da Empresa"}</p>
          </div>
          <div className="bg-pink-50 p-6 rounded-lg border-l-4 border-pink-600">
            <h3 className="text-lg font-semibold text-pink-600 mb-3">
              Faturado para
            </h3>
            <p className="font-bold">{billTo.name || "Nome do Cliente"}</p>
            <p className="text-gray-600">{billTo.address || "Endereço do Cliente"}</p>
            <p className="text-gray-600">{billTo.phone || "Telefone do Cliente"}</p>
          </div>
        </div>

        {/* Tabela de itens */}
        <div className="w-full mb-8 overflow-hidden rounded-lg border border-gray-300">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-pink-600 text-white">
                <tr>
                  <th className="p-3 text-left">Item/Descrição do Item</th>
                  <th className="p-3 text-center">Qtde.</th>
                  <th className="p-3 text-center">Preço Unit.</th>
                  <th className="p-3 text-right">Valor</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item, index) => (
                  <tr key={index} className={index % 2 === 0 ? "bg-pink-50" : "bg-white"}>
                    <td className="p-3">
                      <div className="font-medium">{item.name || "Nome do Item"}</div>
                      {item.description && (
                        <div className="text-sm text-gray-600">{item.description}</div>
                      )}
                    </td>
                    <td className="p-3 text-center">{item.quantity || 1}</td>
                    <td className="p-3 text-center">
                      {formatCurrency(item.amount || 0, selectedCurrency)}
                    </td>
                    <td className="p-3 text-right font-medium">
                      {formatCurrency(item.total || 0, selectedCurrency)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Seção de totais */}
        <div className="flex justify-end mb-8">
          <div className="w-1/2 bg-pink-50 p-4 rounded-lg border border-pink-200">
            <div className="flex justify-between mb-2 text-gray-700">
              <span>Subtotal:</span>
              <span>{formatCurrency(subTotal, selectedCurrency)}</span>
            </div>
            {taxPercentage > 0 && (
              <div className="flex justify-between mb-2 text-gray-700">
                <span>Taxa ({taxPercentage}%):</span>
                <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg mt-2 pt-2 border-t border-pink-300">
              <span>Total:</span>
              <span className="text-pink-600">
                {formatCurrency(grandTotal, selectedCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Observações */}
        {notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-pink-600 mb-2">
              Observações
            </h3>
            <p className="text-gray-600">{notes}</p>
          </div>
        )}

        {/* PIX QR Code */}
        <div className="flex justify-center mb-8">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">PIX - Escaneie para Pagar</p>
            <PixQRCode
              pixData={pixData}
              grandTotal={grandTotal}
              size={150}
              className="border"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-300 text-center">
          <p className="text-pink-600 font-semibold">Obrigado pela preferência!</p>
          <p className="text-sm text-gray-500 mt-2">{yourCompany.name || "Sua Empresa"}</p>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template9;
