import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template9 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold text-orange-600 mb-2">{translations.invoice}</h1>
            <h2 className="text-xl font-bold">
              {yourCompany.name || "Nome da Sua Empresa"}
            </h2>
            <p>{yourCompany.address || "Endereço da Empresa"}</p>
            <p>{yourCompany.phone || "Telefone da Empresa"}</p>
          </div>
          <div className="text-right">
            <p>
              <span className="font-semibold">Número da Fatura:</span>{" "}
              {invoice.number || "N/A"}
            </p>
            <p>
              <span className="font-semibold">Data da Fatura:</span>{" "}
              {invoice.date
                ? formatDateBR(invoice.date)
                : "N/A"}
            </p>
            <p>
              <span className="font-semibold">Data de Vencimento:</span>{" "}
              {invoice.paymentDate
                ? formatDateBR(invoice.paymentDate)
                : "N/A"}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-8">
          <div className="bg-orange-50 p-4 rounded">
            <h3 className="text-lg font-semibold text-orange-600 mb-2">
              Faturado por
            </h3>
            <p>{yourCompany.name || "Nome da Sua Empresa"}</p>
            <p>{yourCompany.address || "Endereço da Sua Empresa"}</p>
          </div>
          <div className="bg-orange-50 p-4 rounded">
            <h3 className="text-lg font-semibold text-orange-600 mb-2">
              Faturado para
            </h3>
            <p>{billTo.name || "Nome do Cliente"}</p>
            <p>{billTo.address || "Endereço do Cliente"}</p>
          </div>
        </div>

        <div className="w-full mb-8 overflow-hidden rounded-lg border border-orange-50">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-orange-600 text-white">
                <tr>
                  <th className="p-2 text-left">Item #/Descrição do Item</th>
                  <th className="p-2 text-right">Qtde.</th>
                  <th className="p-2 text-right">Preço Unit.</th>
                  <th className="p-2 text-right">{translations.amount}</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item, index) => (
                  <tr key={index} className="bg-orange-50">
                    <td className="p-2">{item.name || "Nome do Item"}</td>
                    <td className="p-2 text-right">{item.quantity || 0}</td>
                    <td className="p-2 text-right">
                      {formatCurrency(item.amount || 0, selectedCurrency)}
                    </td>
                    <td className="p-2 text-right">
                      {formatCurrency(
                        (item.quantity || 0) * (item.amount || 0),
                        selectedCurrency
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div className="flex justify-end mb-8">
          <div className="w-1/2 bg-orange-50 p-3 rounded-lg">
            <div className="flex justify-between mb-2">
              <span>{translations.subtotal}:</span>
              <span>{formatCurrency(subTotal, selectedCurrency)}</span>
            </div>
            {taxPercentage > 0 && (
              <div className="flex justify-between mb-2">
                <span>Imposto ({taxPercentage}%):</span>
                <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg mt-2 text-orange-600">
              <span>{translations.total}:</span>
              <span className="text-orange-600">
                {formatCurrency(grandTotal, selectedCurrency)}
              </span>
            </div>
          </div>
        </div>
        {notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-orange-600 mb-2">
              Remarks
            </h3>
            <p>{notes}</p>
          </div>
        )}
        
        <div className="flex justify-center mb-8">
          <PixQRCode 
            pixData={pixData}
            grandTotal={grandTotal}
            size={150}
            className="mt-4"
          />
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template9;
