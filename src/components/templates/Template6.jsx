import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template6 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-gray-50 p-8 max-w-4xl mx-auto">
        {/* Header com status Unpaid */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-4xl font-normal text-blue-600 mb-2">{translations.invoice}</h1>
            <div className="bg-red-500 text-white px-3 py-1 rounded inline-block">
              <span className="font-bold">Unpaid</span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-right">
              <p className="font-semibold">Billed by</p>
              <p className="font-bold">Foobar Labs</p>
              <p>46, Raghuveer Dham Society</p>
              <p>Surat, Gujarat, India - 394710</p>
            </div>
          </div>
        </div>

        {/* Seções Billed to, Invoice Details, Payment Record */}
        <div className="grid grid-cols-3 gap-8 mb-8">
          <div>
            <h3 className="font-semibold text-gray-600 mb-2">Billed to</h3>
            <p className="font-bold">{billTo.name || "Studio Den"}</p>
            <p className="text-sm">{billTo.address || "305, 3rd Floor Orion mall, Bengaluru, Karnataka, India - 560055"}</p>
            <div className="mt-4 text-sm">
              <p><span className="font-semibold">GST:</span> {billTo.gst || "29**********Z26"}</p>
              <p><span className="font-semibold">PAN:</span> {billTo.pan || "**********"}</p>
            </div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-600 mb-2">Invoice Details</h3>
            <div className="text-sm space-y-1">
              <p><span className="font-semibold">Invoice #:</span> {invoice.number || "003"}</p>
              <p><span className="font-semibold">Invoice Date:</span> {invoice.date ? formatDateBR(invoice.date) : "FEB 19, 2020"}</p>
              <p><span className="font-semibold">Due Date:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "FEB 19, 2020"}</p>
            </div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-600 mb-2">Payment Record</h3>
            <div className="text-sm space-y-1">
              <p><span className="font-semibold">Paid Amount:</span> ₹0</p>
              <p><span className="font-semibold text-blue-600 text-xl">Due Amount:</span> <span className="text-blue-600 text-xl font-bold">{formatCurrency(grandTotal, selectedCurrency)}</span></p>
            </div>
          </div>
        </div>

        {/* Tabela de itens */}
        <table className="w-full mb-8 border border-gray-300">
          <thead>
            <tr className="bg-blue-600 text-white">
              <th className="p-3 text-left">Item #/Item description</th>
              <th className="p-3 text-center">HSN</th>
              <th className="p-3 text-center">Qty.</th>
              <th className="p-3 text-center">GST</th>
              <th className="p-3 text-center">Taxable Amount</th>
              <th className="p-3 text-center">SGST</th>
              <th className="p-3 text-center">CGST</th>
              <th className="p-3 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index} className="border-b">
                <td className="p-3">{item.name || "Basic Web Development"}</td>
                <td className="p-3 text-center">02</td>
                <td className="p-3 text-center">{item.quantity || 10}</td>
                <td className="p-3 text-center">{taxPercentage}%</td>
                <td className="p-3 text-center">{formatCurrency(item.amount || 10000, selectedCurrency)}</td>
                <td className="p-3 text-center">₹900</td>
                <td className="p-3 text-center">₹900</td>
                <td className="p-3 text-right font-semibold">{formatCurrency((item.quantity || 0) * (item.amount || 0), selectedCurrency)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Seção inferior */}
        <div className="grid grid-cols-2 gap-8">
          {/* Coluna esquerda */}
          <div>
            <div className="mb-6">
              <p><span className="font-semibold">Country of supply:</span> India</p>
              <p><span className="font-semibold">Place of supply:</span> Gujarat</p>
            </div>
            
            <div className="mb-6">
              <h4 className="font-semibold mb-2">Invoice total in words</h4>
              <p className="text-blue-600 font-semibold">Forty Two thousand Four Hundred and Eighty</p>
            </div>
            
            <div className="mb-6">
              <h4 className="font-semibold mb-2">Terms and Conditions</h4>
              <p className="text-sm mb-2">1. Please pay within 15 days from the date of invoice, overdue interest @ 14% will be charged on delayed payments.</p>
              <p className="text-sm">2. Please quote invoice number when remitting funds.</p>
            </div>
            
            <div className="mb-6">
              <h4 className="font-semibold mb-2">Additional Notes</h4>
              <p className="text-sm">{notes || "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here'."}</p>
            </div>
            
            <div className="text-center">
              <p className="text-xs">For any enquiries, email us on <span className="font-semibold"><EMAIL></span> or call us on</p>
              <p className="text-xs font-semibold">+91 98765 43210</p>
            </div>
          </div>
          
          {/* Coluna direita - Totais */}
          <div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between">
                <span>Sub Total</span>
                <span>{formatCurrency(subTotal, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>Discount(10%)</span>
                <span>- {formatCurrency(subTotal * 0.1, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>Taxable Amount</span>
                <span>{formatCurrency(subTotal * 0.9, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>SGST</span>
                <span>{formatCurrency(taxAmount / 2, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>CGST</span>
                <span>{formatCurrency(taxAmount / 2, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between font-bold text-xl border-t pt-2 mt-4">
                <span>Total Due</span>
                <span className="text-blue-600">{formatCurrency(grandTotal, selectedCurrency)}</span>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Bank & Payment Details</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><span className="font-semibold">Account Holder Name:</span> {bankDetails.accountHolderName || "Foobar Labs"}</p>
                  <p><span className="font-semibold">Account Number:</span> {bankDetails.accountNumber || "************"}</p>
                  <p><span className="font-semibold">IFSC:</span> {bankDetails.ifsc || "HDFCOO18159"}</p>
                  <p><span className="font-semibold">Account Type:</span> {bankDetails.accountType || "Savings"}</p>
                  <p><span className="font-semibold">Bank:</span> {bankDetails.bankName || "HDFC Bank"}</p>
                  <p><span className="font-semibold">UPI:</span> {bankDetails.upi || "foobarlabs@okhdfc"}</p>
                </div>
                <div className="flex justify-center">
                  <div className="text-center">
                    <p className="text-xs mb-2">UPI - Scan to Pay</p>
                    <PixQRCode 
                      pixData={pixData}
                      grandTotal={grandTotal}
                      size={120}
                      className="border"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template6;
