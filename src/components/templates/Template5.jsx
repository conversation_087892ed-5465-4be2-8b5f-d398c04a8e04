import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template5 = ({ data = {} }) => {
  const { billTo = {}, shipTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, selectedCurrency, pixData, bankDetails = {}, notes = '', transportDetails = {} } = data;

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        {/* Header com logo */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <div className="flex items-center mb-4">
              <div className="bg-black text-white px-2 py-1 rounded mr-2">
                <span className="font-bold">F</span>
              </div>
              <div>
                <h2 className="text-lg font-bold">FOOBAR</h2>
                <p className="text-lg font-bold">LABS</p>
              </div>
            </div>
          </div>
          <div className="text-right">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{translations.invoice}</h1>
            <div className="text-sm space-y-1">
              <p><span className="font-semibold">Invoice #:</span> {invoice.number || "003"}</p>
              <p><span className="font-semibold">Invoice Date:</span> {invoice.date ? formatDateBR(invoice.date) : "NOV 20, 2021"}</p>
              <p><span className="font-semibold">Due Date:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "NOV 30, 2021"}</p>
            </div>
          </div>
        </div>

        {/* Seções Billed by, Billed to */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="font-bold text-gray-800 mb-2">Billed by</h3>
            <p className="font-bold">Foobar Labs</p>
            <p className="text-sm">52-59, HSR Layout, 3rd Floor Orion mall,</p>
            <p className="text-sm">Bengaluru, Karnataka, India - 560055</p>
            <div className="mt-4 text-sm space-y-1">
              <p><span className="font-semibold">Email:</span> 29VGCED1234KZ26</p>
              <p><span className="font-semibold">Phone:</span> ****** 423 3245</p>
            </div>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-2">Billed to</h3>
            <p className="font-bold">{billTo.name || "Donald Clinton"}</p>
            <p className="text-sm">{billTo.address || "e-803/ Atlanta Mall, Bengaluru, Karnataka, India - 560063"}</p>
            <div className="mt-4 text-sm space-y-1">
              <p><span className="font-semibold">Email:</span> {billTo.email || "29VGCED1234KZ26"}</p>
              <p><span className="font-semibold">Phone:</span> {billTo.phone || "****** 423 3245"}</p>
            </div>
          </div>
        </div>

        {/* Seções Shipped From, Shipped To, Transport Details */}
        <div className="grid grid-cols-3 gap-6 mb-8">
          <div>
            <h3 className="font-bold text-gray-800 mb-2">Shipped From</h3>
            <p className="font-bold">Foobar Labs</p>
            <p className="text-sm">52-59, HSR Layout, 3rd Floor Orion mall,</p>
            <p className="text-sm">Bengaluru, Karnataka, India - 560055</p>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-2">Shipped To</h3>
            <p className="font-bold">{shipTo.name || "Donald Clinton"}</p>
            <p className="text-sm">{shipTo.address || "e-803/ Atlanta Mall, Bengaluru, Karnataka, India - 560063"}</p>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-2">Transport Details</h3>
            <p className="text-sm">{transportDetails.company || "Federal Express Corporation (By Air)"}</p>
            <p className="text-sm"><span className="font-semibold">Challan Date:</span> {transportDetails.challanDate || "November 20, 2021"}</p>
            <p className="text-sm"><span className="font-semibold">Challan number:</span> {transportDetails.challanNumber || "52/KNT/0045"}</p>
          </div>
        </div>

        {/* Tabela de itens */}
        <table className="w-full mb-8 border border-gray-400">
          <thead className="bg-gray-800 text-white">
            <tr>
              <th className="p-3 text-left">Service Description</th>
              <th className="p-3 text-center">Qty.</th>
              <th className="p-3 text-center">Unit Price/Rate</th>
              <th className="p-3 text-center">Tax Rate</th>
              <th className="p-3 text-center">Tax Amount</th>
              <th className="p-3 text-right">Total Amount</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index} className="border-b">
                <td className="p-3">
                  <p className="font-semibold">{`${index + 1}. ${item.name || "Printing Circuit Board"}`}</p>
                  <p className="text-sm text-gray-600">{item.description || "It contains: 1x DRAM, 4x HTS, Made in Japan"}</p>
                </td>
                <td className="p-3 text-center">{item.quantity || 50}</td>
                <td className="p-3 text-center">{formatCurrency(item.amount || 100, selectedCurrency)}</td>
                <td className="p-3 text-center">{taxPercentage}%</td>
                <td className="p-3 text-center">{formatCurrency(taxAmount || 500, selectedCurrency)}</td>
                <td className="p-3 text-right font-semibold">{formatCurrency((item.quantity || 0) * (item.amount || 0), selectedCurrency)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Seção inferior */}
        <div className="grid grid-cols-2 gap-8">
          {/* Coluna esquerda - Terms and Conditions, Additional Notes, Bank Details */}
          <div>
            <div className="mb-6">
              <h4 className="font-semibold mb-2">Terms and Conditions</h4>
              <p className="text-sm mb-2">1. Please pay within 15 days from the date of invoice, overdue interest @ 14% will be charged on delayed payments.</p>
              <p className="text-sm">2. Please quote invoice number when remitting funds.</p>
            </div>
            
            <div className="mb-6">
              <h4 className="font-semibold mb-2">Additional Notes</h4>
              <p className="text-sm">{notes || "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here'."}</p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Bank & Payment Details</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><span className="font-semibold">Account Holder Name:</span> {bankDetails.accountHolderName || "Foobar Labs"}</p>
                  <p><span className="font-semibold">Account Number:</span> {bankDetails.accountNumber || "***********"}</p>
                  <p><span className="font-semibold">IFSC:</span> {bankDetails.ifsc || "SBIN0018159"}</p>
                  <p><span className="font-semibold">Account Type:</span> {bankDetails.accountType || "Current"}</p>
                  <p><span className="font-semibold">Bank Name:</span> {bankDetails.bankName || "State Bank of India"}</p>
                  <p><span className="font-semibold">UPI:</span> {bankDetails.upi || "foobarlabs@oksbi"}</p>
                </div>
                <div className="flex justify-center">
                  <div className="text-center">
                    <p className="text-xs mb-2">PIX - Escaneie para Pagar</p>
                    <PixQRCode 
                      pixData={pixData}
                      grandTotal={grandTotal}
                      size={120}
                      className="border"
                    />
                  </div>
                </div>
              </div>
              
              <div className="mt-4 text-center">
                <p className="text-xs mb-2">For any enquiries, email <NAME_EMAIL> or call us on +91 98765-43210</p>
              </div>
            </div>
          </div>
          
          {/* Coluna direita - Totais e Assinatura */}
          <div>
            <div className="border border-gray-300 p-4 mb-6">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Amount</span>
                  <span>{formatCurrency(subTotal, selectedCurrency)}</span>
                </div>
                <div className="flex justify-between">
                  <span>TAX</span>
                  <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping Cost</span>
                  <span>₹140</span>
                </div>
              </div>
              
              <div className="bg-blue-600 text-white p-2 mt-4 text-center">
                <p className="font-bold">Total Due Amount</p>
                <p className="font-bold text-lg">{formatCurrency(grandTotal, selectedCurrency)}</p>
              </div>
              
              <div className="mt-4 text-xs">
                <p className="font-semibold mb-1">Invoice Total in words:</p>
                <p>Six Thousand Three Hundred Dollars Only</p>
              </div>
            </div>
            
            {/* Assinatura */}
            <div className="text-right">
              <div className="border-b-2 border-gray-400 w-32 mb-2 ml-auto"></div>
              <p className="font-bold">Authorized Signature</p>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template5;
