import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template5 = ({ data = {} }) => {
  const { billTo = {}, shipTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, selectedCurrency, pixData, bankDetails = {}, notes = '', transportDetails = {} } = data;

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        {/* Header com design baseado na imagem template-5 */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <div className="flex items-center mb-4">
              <div className="bg-black text-white px-2 py-1 rounded mr-2">
                <span className="font-bold">F</span>
              </div>
              <div>
                <h2 className="text-lg font-bold">FOOBAR</h2>
                <p className="text-lg font-bold">LABS</p>
              </div>
            </div>
          </div>
          <div className="text-right">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">FATURA</h1>
            <div className="text-sm space-y-1 text-gray-600">
              <p><span className="font-semibold">Fatura #:</span> {invoice.number || "003"}</p>
              <p><span className="font-semibold">Data da Fatura:</span> {invoice.date ? formatDateBR(invoice.date) : "20 NOV, 2021"}</p>
              <p><span className="font-semibold">Data de Vencimento:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "30 NOV, 2021"}</p>
            </div>
          </div>
        </div>

        {/* Seções Faturado por e Faturado para */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="font-bold text-gray-800 mb-3">Faturado por</h3>
            <p className="font-bold text-lg">{yourCompany.name || "Foobar Labs"}</p>
            <p className="text-sm text-gray-600">52-59, HSR Layout, 3rd Floor Orion mall,</p>
            <p className="text-sm text-gray-600">Bengaluru, Karnataka, India - 560055</p>
            <div className="mt-4 text-sm space-y-1 text-gray-600">
              <p><span className="font-semibold">Email:</span> {yourCompany.email || "<EMAIL>"}</p>
              <p><span className="font-semibold">Telefone:</span> {yourCompany.phone || "+55 11 99999-9999"}</p>
            </div>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-3">Faturado para</h3>
            <p className="font-bold text-lg">{billTo.name || "Donald Clinton"}</p>
            <p className="text-sm text-gray-600">{billTo.address || "e-803/ Atlanta Mall, Bengaluru, Karnataka, India - 560063"}</p>
            <div className="mt-4 text-sm space-y-1 text-gray-600">
              <p><span className="font-semibold">Email:</span> {billTo.email || "<EMAIL>"}</p>
              <p><span className="font-semibold">Telefone:</span> {billTo.phone || "+55 11 98765-4321"}</p>
            </div>
          </div>
        </div>

        {/* Seções Enviado de, Enviado para, Detalhes do Transporte */}
        <div className="grid grid-cols-3 gap-6 mb-8">
          <div>
            <h3 className="font-bold text-gray-800 mb-3">Enviado de</h3>
            <p className="font-bold">{yourCompany.name || "Foobar Labs"}</p>
            <p className="text-sm text-gray-600">52-59, HSR Layout, 3rd Floor Orion mall,</p>
            <p className="text-sm text-gray-600">Bengaluru, Karnataka, India - 560055</p>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-3">Enviado para</h3>
            <p className="font-bold">{shipTo.name || billTo.name || "Donald Clinton"}</p>
            <p className="text-sm text-gray-600">{shipTo.address || billTo.address || "e-803/ Atlanta Mall, Bengaluru, Karnataka, India - 560063"}</p>
          </div>
          <div>
            <h3 className="font-bold text-gray-800 mb-3">Detalhes do Transporte</h3>
            <p className="text-sm text-gray-600">{transportDetails.company || "Federal Express Corporation (Aéreo)"}</p>
            <p className="text-sm text-gray-600"><span className="font-semibold">Data do Romaneio:</span> {transportDetails.challanDate || "20 de Novembro, 2021"}</p>
            <p className="text-sm text-gray-600"><span className="font-semibold">Número do Romaneio:</span> {transportDetails.challanNumber || "52/KNT/0045"}</p>
          </div>
        </div>

        {/* Tabela de itens */}
        <table className="w-full mb-8 border border-gray-400">
          <thead className="bg-gray-800 text-white">
            <tr>
              <th className="p-3 text-left">Descrição do Serviço</th>
              <th className="p-3 text-center">Qtd.</th>
              <th className="p-3 text-center">Preço Unitário</th>
              <th className="p-3 text-center">Taxa</th>
              <th className="p-3 text-center">Valor da Taxa</th>
              <th className="p-3 text-right">Valor Total</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index} className="border-b border-gray-300">
                <td className="p-3">
                  <p className="font-semibold">{`${index + 1}. ${item.name || "Placa de Circuito Impresso"}`}</p>
                  <p className="text-sm text-gray-600">{item.description || "Contém: 1x DRAM, 4x HTS, Fabricado no Brasil"}</p>
                </td>
                <td className="p-3 text-center">{item.quantity || 1}</td>
                <td className="p-3 text-center">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
                <td className="p-3 text-center">{taxPercentage}%</td>
                <td className="p-3 text-center">{formatCurrency((item.amount || 0) * (taxPercentage / 100), selectedCurrency)}</td>
                <td className="p-3 text-right font-semibold">{formatCurrency(item.total || 0, selectedCurrency)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Seção inferior */}
        <div className="grid grid-cols-2 gap-8">
          {/* Coluna esquerda - Termos e Condições, Observações Adicionais, Detalhes Bancários */}
          <div>
            <div className="mb-6">
              <h4 className="font-semibold mb-2 text-gray-800">Termos e Condições</h4>
              <p className="text-sm mb-2 text-gray-600">1. Favor pagar em até 15 dias a partir da data da fatura. Juros de 14% serão cobrados sobre pagamentos em atraso.</p>
              <p className="text-sm text-gray-600">2. Favor citar o número da fatura ao efetuar o pagamento.</p>
            </div>

            <div className="mb-6">
              <h4 className="font-semibold mb-2 text-gray-800">Observações Adicionais</h4>
              <p className="text-sm text-gray-600">{notes || "É um fato estabelecido que um leitor será distraído pelo conteúdo legível de uma página ao olhar seu layout. O ponto de usar Lorem Ipsum é que ele tem uma distribuição mais ou menos normal de letras."}</p>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-gray-800">Detalhes Bancários e Pagamento</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1 text-gray-600">
                  <p><span className="font-semibold">Nome do Titular:</span> {bankDetails.accountHolderName || "Foobar Labs"}</p>
                  <p><span className="font-semibold">Número da Conta:</span> {bankDetails.accountNumber || "***********"}</p>
                  <p><span className="font-semibold">Agência:</span> {bankDetails.ifsc || "0018159"}</p>
                  <p><span className="font-semibold">Tipo de Conta:</span> {bankDetails.accountType || "Corrente"}</p>
                  <p><span className="font-semibold">Nome do Banco:</span> {bankDetails.bankName || "Banco do Brasil"}</p>
                  <p><span className="font-semibold">PIX:</span> {pixData?.key || "<EMAIL>"}</p>
                </div>
                <div className="flex justify-center">
                  <div className="text-center">
                    <p className="text-xs mb-2 text-gray-600">PIX - Escaneie para Pagar</p>
                    <PixQRCode
                      pixData={pixData}
                      grandTotal={grandTotal}
                      size={120}
                      className="border"
                    />
                  </div>
                </div>
              </div>

              <div className="mt-4 text-center">
                <p className="text-xs mb-2 text-gray-500">Para consultas, envie-nos um e-mail: {yourCompany.email || "<EMAIL>"} ou ligue: {yourCompany.phone || "+55 11 99999-9999"}</p>
              </div>
            </div>
          </div>
          
          {/* Coluna direita - Totais e Assinatura */}
          <div>
            <div className="border border-gray-300 p-4 mb-6">
              <div className="space-y-2 text-sm text-gray-700">
                <div className="flex justify-between">
                  <span>Valor</span>
                  <span>{formatCurrency(subTotal, selectedCurrency)}</span>
                </div>
                <div className="flex justify-between">
                  <span>TAXA</span>
                  <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Custo de Envio</span>
                  <span>{formatCurrency(140, selectedCurrency)}</span>
                </div>
              </div>

              <div className="bg-blue-600 text-white p-2 mt-4 text-center">
                <p className="font-bold">Valor Total Devido</p>
                <p className="font-bold text-lg">{formatCurrency(grandTotal, selectedCurrency)}</p>
              </div>

              <div className="mt-4 text-xs text-gray-600">
                <p className="font-semibold mb-1">Total da fatura por extenso:</p>
                <p>Seis mil e trezentos reais</p>
              </div>
            </div>

            {/* Assinatura */}
            <div className="text-right">
              <div className="border-b-2 border-gray-400 w-32 mb-2 ml-auto"></div>
              <p className="font-bold text-gray-800">Assinatura Autorizada</p>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template5;
