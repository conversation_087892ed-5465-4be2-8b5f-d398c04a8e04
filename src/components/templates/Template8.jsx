import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template8 = ({ data }) => {
  const { billTo = {}, shipTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8 max-w-4xl mx-auto">
        {/* Header com design baseado na imagem template-8 */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold text-indigo-600 mb-2">FATURA</h1>
            <div className="text-sm space-y-1 text-gray-600">
              <p><span className="font-semibold">Fatura #:</span> {invoice.number || "008"}</p>
              <p><span className="font-semibold">Data da Fatura:</span> {invoice.date ? formatDateBR(invoice.date) : "20 JUL, 2019"}</p>
              <p><span className="font-semibold">Data de Vencimento:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "30 JUL, 2019"}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center justify-end mb-4">
              <div className="bg-indigo-600 text-white px-2 py-1 rounded mr-2">
                <span className="font-bold">F</span>
              </div>
              <div className="text-right">
                <h2 className="text-lg font-bold text-gray-800">FOOBAR</h2>
                <p className="text-lg font-bold text-gray-800">LABS</p>
              </div>
            </div>
          </div>
        </div>

        {/* Seções de informações */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Faturado para</h3>
            <p className="font-bold text-lg">{billTo.name || "Cliente Exemplo"}</p>
            <p className="text-gray-600">{billTo.address || "Endereço do Cliente"}</p>
            <p className="text-gray-600">{billTo.phone || "Telefone do Cliente"}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Detalhes da Fatura</h3>
            <div className="text-gray-600 space-y-1">
              <p><span className="font-semibold">Número da Fatura:</span> {invoice.number || "008"}</p>
              <p><span className="font-semibold">Data da Fatura:</span> {invoice.date ? formatDateBR(invoice.date) : "20 JUL, 2019"}</p>
              <p><span className="font-semibold">Data de Vencimento:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "30 JUL, 2019"}</p>
            </div>
          </div>
        </div>

        {/* Tabela de itens */}
        <table className="w-full mb-8 border border-gray-300">
          <thead className="bg-indigo-600 text-white">
            <tr>
              <th className="p-3 text-left">Item</th>
              <th className="p-3 text-center">Quantidade</th>
              <th className="p-3 text-center">Preço Unit.</th>
              <th className="p-3 text-right">Valor</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
              >
                <td className="p-3">
                  <div className="font-medium">{item.name || `Item ${index + 1}`}</div>
                  {item.description && (
                    <div className="text-sm text-gray-600">{item.description}</div>
                  )}
                </td>
                <td className="p-3 text-center">{item.quantity || 1}</td>
                <td className="p-3 text-center">
                  {formatCurrency(item.amount || 0, selectedCurrency)}
                </td>
                <td className="p-3 text-right font-medium">
                  {formatCurrency(item.total || 0, selectedCurrency)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Seção de totais */}
        <div className="flex justify-end mb-8">
          <div className="w-1/2">
            <div className="flex justify-between mb-2 text-gray-700">
              <span>Subtotal:</span>
              <span>{formatCurrency(subTotal, selectedCurrency)}</span>
            </div>
            {taxPercentage > 0 && (
              <div className="flex justify-between mb-2 text-gray-700">
                <span>Taxa ({taxPercentage}%):</span>
                <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg mt-2 border-t pt-2">
              <span>Total a Pagar:</span>
              <span className="text-indigo-600">
                {formatCurrency(grandTotal, selectedCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Seção de observações */}
        {notes && (
          <div className="mt-8 border-t pt-4">
            <h3 className="text-lg font-semibold mb-2 text-gray-800">Observações:</h3>
            <p className="text-gray-600">{notes}</p>
          </div>
        )}

        {/* PIX QR Code */}
        <div className="flex justify-center mt-6">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">PIX - Escaneie para Pagar</p>
            <PixQRCode
              pixData={pixData}
              grandTotal={grandTotal}
              size={140}
              className="border"
            />
          </div>
        </div>

        {/* Footer com informações da empresa */}
        <div className="mt-8 pt-6 border-t border-gray-300">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-indigo-600">FATURA</h1>
              <p className="text-sm text-gray-500">Obrigado pela preferência!</p>
            </div>
            <div className="text-right text-gray-600">
              <h2 className="text-lg font-bold">{yourCompany.name || "Sua Empresa"}</h2>
              <p className="text-sm">{yourCompany.address || "Endereço da Empresa"}</p>
              <p className="text-sm">{yourCompany.phone || "Telefone da Empresa"}</p>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template8;
