import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template8 = ({ data }) => {
  const { billTo, shipTo, invoice, yourCompany, items, taxPercentage, taxAmount, subTotal, grandTotal, notes, selectedCurrency, pixData } = data;

  return (
    <BaseTemplate data={data}>
      <div
        className="bg-gray-100 w-full h-full flex flex-col"
        style={{ margin: "0", padding: "16px" }}
      >
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold mb-2">Faturado para</h3>
            <p className="font-bold">{billTo.name}</p>
            <p>{billTo.address}</p>
            <p>{billTo.phone}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">Detalhes da Fatura</h3>
            <p>
              <span className="font-semibold">Número da Fatura:</span> {invoice.number}
            </p>
            <p>
              <span className="font-semibold">Data da Fatura:</span>{" "}
              {invoice.date}
            </p>
            <p>
              <span className="font-semibold">Data de Vencimento:</span>{" "}
              {invoice.paymentDate}
            </p>
          </div>
        </div>

        <table className="w-full mb-8">
          <thead style={{ backgroundColor: "#3C8BF6", color: "white" }}>
            <tr>
              <th className="p-2 text-left">{translations.item}</th>
              <th className="p-2 text-right">{translations.quantity}</th>
              <th className="p-2 text-right">Preço Unit.</th>
              <th className="p-2 text-right">{translations.amount}</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
              >
                <td className="p-2">{item.name}</td>
                <td className="p-2 text-right">{item.quantity}</td>
                <td className="p-2 text-right">
                  {formatCurrency(item.amount, selectedCurrency)}
                </td>
                <td className="p-2 text-right">
                  {formatCurrency(item.quantity * item.amount, selectedCurrency)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="flex justify-end mb-8">
          <div className="w-1/2">
            <div className="flex justify-between mb-2">
              <span>{translations.subtotal}:</span>
              <span>{formatCurrency(subTotal, selectedCurrency)}</span>
            </div>
            {taxPercentage > 0 && (
              <div className="flex justify-between mb-2">
                <span>Imposto ({taxPercentage}%):</span>
                <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg mt-2">
              <span>Total a Pagar:</span>
              <span style={{ color: "#3C8BF6" }}>
                {formatCurrency(grandTotal, selectedCurrency)}
              </span>
            </div>
          </div>
        </div>

        {notes && (
          <div className="mt-8 border-t pt-4">
            <h3 className="text-lg font-semibold mb-2">{translations.notes}:</h3>
            <p>{notes}</p>
          </div>
        )}
        
        <div className="flex justify-center mt-6">
          <PixQRCode 
            pixData={pixData}
            grandTotal={grandTotal}
            size={140}
            className="mt-2"
          />
        </div>
        <footer className="mt-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-4xl font-bold" style={{ color: "#3C8BF6" }}>
              {translations.invoice}
            </h1>
            <div className="text-right">
              <h2 className="text-xl font-bold">{yourCompany.name}</h2>
              <p>{yourCompany.address}</p>
              <p>{yourCompany.phone}</p>
            </div>
          </div>
        </footer>
      </div>
    </BaseTemplate>
  );
};

export default Template8;
