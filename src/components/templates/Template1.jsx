import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { translations, formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template1 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white max-w-4xl mx-auto">
        {/* Header azul */}
        <div className="bg-blue-600 text-white p-6">
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center mb-4">
                <div className="bg-white text-blue-600 px-3 py-1 rounded mr-3">
                  <span className="font-bold text-lg">F</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold">FOOBAR</h1>
                  <p className="text-sm">LABS</p>
                </div>
              </div>
              <p className="text-sm">{yourCompany.name || "Vero Mora Pharmacy"}</p>
              <p className="text-sm">{yourCompany.address || "Juno street, Baba Nagar, Hyderabad, Andhra Pradesh"}</p>
              <p className="text-sm">Pin 500072</p>
              <p className="text-sm">CIN: 23AABYG3456F</p>
              <p className="text-sm">PAN: U60100TG2345</p>
            </div>
            <div className="text-right">
              <p className="text-sm mb-1">Faturar Para</p>
              <p className="text-sm">{billTo.name || "Studio Den"}</p>
              <p className="text-sm">{billTo.address || "305, 3rd Floor Orion mall, Bengaluru"}</p>
              <p className="text-sm">Karnataka, India - 560055</p>
              <div className="bg-white text-blue-600 px-3 py-1 rounded text-sm font-bold mt-2">Valor Devido</div>
            </div>
          </div>
          
          <div className="grid grid-cols-4 gap-4 mt-6 text-sm">
            <div>
              <p className="font-semibold">NÚMERO DE REGISTRO</p>
              <p>{invoice.number || "R12334568"}</p>
            </div>
            <div>
              <p className="font-semibold">DATA DA FATURA</p>
              <p>{invoice.date ? formatDateBR(invoice.date) : "30 de dezembro de 2024"}</p>
            </div>
            <div>
              <p className="font-semibold">DATA DE VENCIMENTO</p>
              <p>{invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "30 de dezembro de 2024"}</p>
            </div>
            <div>
              <p className="font-semibold">VALOR TOTAL</p>
              <p>R$9.480</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Seção de produtos */}
          <div className="mb-6">
            <h3 className="font-semibold text-blue-600 mb-4">ITENS FATURADOS E DESCRIÇÕES</h3>
            
            <table className="w-full">
              <thead>
                <tr className="bg-blue-600 text-white">
                  <th className="p-3 text-left">ITENS</th>
                  <th className="p-3 text-center">QTD</th>
                  <th className="p-3 text-center">VALOR UNIT.</th>
                  <th className="p-3 text-center">GST</th>
                  <th className="p-3 text-right">VALOR</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-3">
                      <p className="font-semibold">{item.name || "Anti aging medicine"}</p>
                      <p className="text-sm text-gray-600">{item.description || "General anti aging medicine designed to improve the health..."}</p>
                    </td>
                    <td className="p-3 text-center">{item.quantity || "02"}</td>
                    <td className="p-3 text-center">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
                    <td className="p-3 text-center">{taxPercentage}%</td>
                    <td className="p-3 text-right font-semibold">{formatCurrency((item.quantity || 0) * (item.amount || 0), selectedCurrency)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Seção inferior com QR Code e totais */}
          <div className="grid grid-cols-2 gap-8">
            {/* Coluna esquerda - QR Code e detalhes bancários */}
            <div>
              <h3 className="font-semibold text-blue-600 mb-4">BANK AND PAYMENT DETAILS</h3>
              
              <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                <div>
                  <p><span className="font-semibold">Account Holder Name:</span> {bankDetails.accountHolderName || "Foobar Labs"}</p>
                  <p><span className="font-semibold">Account Number:</span> {bankDetails.accountNumber || "************"}</p>
                  <p><span className="font-semibold">IFSC:</span> {bankDetails.ifsc || "SBIN0018159"}</p>
                  <p><span className="font-semibold">Account Type:</span> {bankDetails.accountType || "Savings"}</p>
                  <p><span className="font-semibold">Bank:</span> {bankDetails.bankName || "State Bank of India"}</p>
                  <p><span className="font-semibold">UPI:</span> {bankDetails.upi || "foobarlabs@okasbi"}</p>
                </div>
                <div className="flex justify-center">
                  <div className="bg-gray-100 p-2 rounded">
                    <p className="text-xs text-center mb-2">UPI - Scan to Pay</p>
                    <PixQRCode 
                      pixData={pixData}
                      grandTotal={grandTotal}
                      size={120}
                      className="border"
                    />
                  </div>
                </div>
              </div>
              
              <div className="text-sm">
                <h4 className="font-semibold mb-2">TERMOS E CONDIÇÕES</h4>
                <p className="mb-2">Please pay within 15 days from the date of invoice, overdue interest @ 14% will be charged on delayed payments.</p>
                <p>Please quote invoice number when remitting funds.</p>
              </div>
              
              <div className="text-sm mt-4">
                <h4 className="font-semibold mb-2">OBSERVAÇÕES ADICIONAIS</h4>
                <p>{notes || "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here'."}</p>
              </div>
            </div>
            
            {/* Coluna direita - Totais */}
            <div>
              <div className="bg-blue-50 p-4 rounded">
                <h3 className="font-semibold text-blue-600 mb-4">VALORES TOTAIS DA FATURA</h3>
                
                <div className="space-y-2 text-sm">
                  <div className="text-center bg-blue-600 text-white p-2 rounded font-bold text-lg">
                    {formatCurrency(grandTotal, selectedCurrency)}
                  </div>
                  
                  <p className="text-center text-sm mt-2">
                    Valor por extenso em palavras
                  </p>
                  
                  <div className="text-xs mt-4">
                    <p><span className="font-semibold">Para consultas, envie-nos um e-mail:</span> <EMAIL></p>
                    <p><span className="font-semibold">ou ligue para:</span> +91 98765 43210</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template1;
