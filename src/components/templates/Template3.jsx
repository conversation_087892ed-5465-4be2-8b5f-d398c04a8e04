import React from 'react';
import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { formatDateBR } from '../../utils/locale';
import PixQRCode from '../PixQRCode';

const Template3 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxPercentage = 0, taxAmount = 0, subTotal = 0, grandTotal = 0, notes = '', selectedCurrency, pixData, bankDetails = {} } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white max-w-4xl mx-auto">
        {/* Header com design minimalista baseado na imagem template-3 */}
        <div className="p-8">
          <div className="flex justify-between items-start mb-8">
            <div>
              <div className="bg-orange-500 text-white px-3 py-2 rounded mb-4 inline-block">
                <span className="font-bold text-lg">{yourCompany.name ? yourCompany.name.charAt(0) : "S"}</span>
              </div>
              <h1 className="text-xl font-bold text-gray-800 mb-2">{yourCompany.name || "SEMPURNA, INC."}</h1>
              <p className="text-sm text-gray-600">{yourCompany.businessType || "DESIGN STUDIO"}</p>
              <p className="text-xs text-gray-500">{yourCompany.name || "Sempurna Inc"}, {formatDateBR(invoice.date) || "10 Janeiro 2018"}</p>
            </div>
            <div className="text-right">
              <h2 className="text-4xl font-bold text-gray-800 mb-4">FATURA</h2>
              <p className="text-lg font-semibold text-orange-500">{invoice.number || "#*********"}</p>
            </div>
          </div>

          {/* Seção de informações do cliente */}
          <div className="mb-8">
            <div className="bg-gray-100 p-6 rounded mb-4">
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <h3 className="font-bold text-gray-800 mb-3">FATURAR PARA:</h3>
                  <p className="font-semibold text-lg">{billTo.name || "Klabber Inc."}</p>
                  <p className="text-sm text-gray-600">{billTo.address || "770 5th Avenue, New Road"}</p>
                  <p className="text-sm text-gray-600">{billTo.city || "New York City"}</p>
                  {billTo.phone && <p className="text-sm text-gray-600">{billTo.phone}</p>}
                  {billTo.email && <p className="text-sm text-gray-600">{billTo.email}</p>}
                </div>
                <div className="text-right">
                  <h3 className="font-bold text-gray-800 mb-3">Informações da Data</h3>
                  <p><span className="font-semibold">Número da Fatura:</span> {invoice.number || "WV11B2020180"}</p>
                  <p><span className="font-semibold">Data:</span> {invoice.date ? formatDateBR(invoice.date) : "02/08/2018"}</p>
                  <p><span className="font-semibold">Vencimento:</span> {invoice.paymentDate ? formatDateBR(invoice.paymentDate) : "02/09/2018"}</p>
                </div>
              </div>
            </div>
          </div>
          {/* Tabela de itens */}
          <table className="w-full mb-8">
            <thead>
              <tr className="bg-orange-500 text-white">
                <th className="p-3 text-left">Nº</th>
                <th className="p-3 text-left">DESCRIÇÃO DO ITEM</th>
                <th className="p-3 text-center">PREÇO</th>
                <th className="p-3 text-center">QTD</th>
                <th className="p-3 text-right">TOTAL</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                  <td className="p-3 font-semibold text-gray-600">{String(index + 1).padStart(2, "0")}.</td>
                  <td className="p-3">
                    <p className="font-semibold text-gray-800">{item.name || "Design de Branding"}</p>
                    <p className="text-sm text-gray-500">{item.description || "Lorem ipsum dolor sit amet, consectetur"}</p>
                  </td>
                  <td className="p-3 text-center text-gray-700">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
                  <td className="p-3 text-center text-gray-700">{item.quantity || 1}</td>
                  <td className="p-3 text-right font-semibold text-gray-800">{formatCurrency(item.total || 0, selectedCurrency)}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Seção inferior */}
          <div className="grid grid-cols-3 gap-8">
            {/* Coluna esquerda - Método de Pagamento */}
            <div>
              <h4 className="font-semibold mb-4 text-gray-800">Método de Pagamento</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p>Conta Bancária</p>
                <p>Transferência Bancária</p>
                <p>PIX</p>
              </div>

              {pixData && pixData.key && (
                <div className="mt-4">
                  <p className="text-xs text-gray-500 mb-2">PIX - Escaneie para Pagar</p>
                  <PixQRCode
                    pixData={pixData}
                    grandTotal={grandTotal}
                    size={100}
                    className="border"
                  />
                </div>
              )}

              <p className="text-xs mt-4 text-gray-500">{notes || "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy nibh. Lorem ipsum dolor sit amet, consectetur adipiscing elit."}</p>
            </div>

            {/* Coluna central - Termos e Condições */}
            <div>
              <h4 className="font-semibold mb-4 text-gray-800">Termos e Condições</h4>
              <p className="text-xs text-gray-500">Favor pagar em até 15 dias a partir da data da fatura. Juros de 14% serão cobrados sobre pagamentos em atraso. Favor citar o número da fatura ao efetuar o pagamento.</p>
            </div>

            {/* Coluna direita - Totais */}
            <div>
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-gray-700">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(subTotal, selectedCurrency)}</span>
                </div>
                <div className="flex justify-between text-gray-700">
                  <span>Taxa ({taxPercentage}%):</span>
                  <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
                </div>
                <div className="flex justify-between text-gray-700">
                  <span>Desconto (5%):</span>
                  <span>- {formatCurrency(subTotal * 0.05, selectedCurrency)}</span>
                </div>
              </div>

              <div className="bg-orange-500 text-white p-3 text-center rounded">
                <p className="font-bold text-lg">Total Geral:</p>
                <p className="font-bold text-xl">{formatCurrency(grandTotal, selectedCurrency)}</p>
              </div>
            </div>
          </div>

          {/* Footer com assinatura */}
          <div className="mt-8 pt-8 border-t border-gray-300">
            <div className="flex justify-between items-end">
              <div>
                <p className="text-orange-500 font-bold text-lg mb-4">Obrigado pela preferência!</p>
                <div className="text-sm text-gray-600">
                  <p>📞 {yourCompany.phone || "01 234 6789"}</p>
                  <p>✉️ {yourCompany.email || "<EMAIL>"}</p>
                </div>
              </div>
              <div className="text-center">
                <div className="border-b-2 border-gray-400 w-32 mb-2"></div>
                <p className="font-bold text-gray-800">{yourCompany.representative || "Steven Joe"}</p>
                <p className="text-sm text-gray-600">Gerente de Contabilidade</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template3;
