import BaseTemplate from './BaseTemplate';
import { formatCurrency } from '../../utils/formatCurrency';
import { formatDateBR } from '../../utils/locale';

const Template3 = ({ data }) => {
  const { billTo = {}, invoice = {}, yourCompany = {}, items = [], taxAmount = 0, subTotal = 0, grandTotal = 0, selectedCurrency } = data || {};

  return (
    <BaseTemplate data={data}>
      <div className="bg-white p-8">
        <div className="flex justify-between items-start mb-8">
          <div>
            <div className="bg-red-600 text-white px-3 py-2 rounded mb-4 inline-block">
              <span className="font-bold text-lg">S</span>
            </div>
            <h1 className="text-xl font-bold text-gray-800 mb-2">SEMPURNA, INC.</h1>
            <p className="text-sm text-gray-600">DESIGN STUDIO</p>
            <p className="text-xs text-gray-500">Sempurna Inc, 10 January 2018</p>
          </div>
          <div className="text-right">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">INVOICE</h2>
            <p className="text-lg font-semibold text-red-600">{invoice.number || "#*********"}</p>
          </div>
        </div>

        <div className="mb-8">
          <div className="bg-gray-100 p-4 rounded mb-4">
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h3 className="font-bold text-gray-800 mb-2">INVOICE TO:</h3>
                <p className="font-semibold">{billTo.name || "Klabber Inc."}</p>
                <p className="text-sm">{billTo.address || "770 5th Avenue, New Road"}</p>
                <p className="text-sm">{billTo.city || "New York City"}</p>
              </div>
              <div className="text-right">
                <h3 className="font-bold text-gray-800 mb-2">Date Information</h3>
                <p><span className="font-semibold">Invoice Number:</span> {invoice.number || "WV11B2020180"}</p>
                <p><span className="font-semibold">Date:</span> {invoice.date ? formatDateBR(invoice.date) : "02/08/2018"}</p>
              </div>
            </div>
          </div>
        </div>
        {/* Tabela de itens */}
        <table className="w-full mb-8">
          <thead>
            <tr className="bg-red-600 text-white">
              <th className="p-3 text-left">NO</th>
              <th className="p-3 text-left">ITEM DESCRIPTION</th>
              <th className="p-3 text-center">PRICE</th>
              <th className="p-3 text-center">QTY</th>
              <th className="p-3 text-right">TOTAL</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                <td className="p-3 font-semibold">{String(index + 1).padStart(2, "0")}.</td>
                <td className="p-3">
                  <p className="font-semibold">{item.name || "Branding Design"}</p>
                  <p className="text-sm text-gray-500">{item.description || "Lorem ipsum dolor sit amet, consectetur"}</p>
                </td>
                <td className="p-3 text-center">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
                <td className="p-3 text-center">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
                <td className="p-3 text-right font-semibold">{formatCurrency(item.amount || 0, selectedCurrency)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Seção inferior */}
        <div className="grid grid-cols-3 gap-8">
          {/* Coluna esquerda - Payment Method */}
          <div>
            <h4 className="font-semibold mb-4">Payment Method</h4>
            <p className="text-sm mb-1">Bank Account</p>
            <p className="text-sm mb-1">Bank Transfer</p>
            <p className="text-sm">Bank Code</p>
            
            <p className="text-xs mt-4">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy nibh. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy nibh.</p>
          </div>
          
          {/* Coluna central - Terms & Condition */}
          <div>
            <h4 className="font-semibold mb-4">Terms & Condition</h4>
            <p className="text-xs">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy nibh Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy nibh. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam nonummy nibh.</p>
          </div>
          
          {/* Coluna direita - Totais */}
          <div>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between">
                <span>Sub Total:</span>
                <span>{formatCurrency(subTotal, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax. Vat (15%):</span>
                <span>{formatCurrency(taxAmount, selectedCurrency)}</span>
              </div>
              <div className="flex justify-between">
                <span>Discount 5%:</span>
                <span>{formatCurrency(0, selectedCurrency)}</span>
              </div>
            </div>
            
            <div className="bg-red-600 text-white p-3 text-center rounded">
              <p className="font-bold text-lg">Grand Total:</p>
              <p className="font-bold text-xl">{formatCurrency(grandTotal, selectedCurrency)}</p>
            </div>
          </div>
        </div>
        
        {/* Footer com assinatura */}
        <div className="mt-8 pt-8 border-t">
          <div className="flex justify-between items-end">
            <div>
              <p className="text-red-600 font-bold text-lg mb-4">Thank you for your business!</p>
              <div className="text-sm">
                <p>📞 01 234 6789</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>
            <div className="text-center">
              <div className="border-b-2 border-gray-400 w-32 mb-2"></div>
              <p className="font-bold">Steven Joe</p>
              <p className="text-sm">Accounting Manager</p>
            </div>
          </div>
        </div>
      </div>
    </BaseTemplate>
  );
};

export default Template3;
