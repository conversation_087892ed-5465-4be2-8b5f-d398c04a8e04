import React, { useState, useEffect } from 'react';
import { generatePixQRCode, validatePixData } from '../utils/pixQRCode';

const PixQRCode = ({ pixData, grandTotal, size = 200, className = "" }) => {
  const [qrCodeDataURL, setQrCodeDataURL] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const generateQRCode = async () => {
      if (!pixData || !validatePixData(pixData)) {
        setQrCodeDataURL(null);
        setError(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const pixDataWithAmount = {
          ...pixData,
          valor: grandTotal || 0,
        };

        const dataURL = await generatePixQRCode(pixDataWithAmount, {
          width: size,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });

        setQrCodeDataURL(dataURL);
      } catch (err) {
        console.error('Erro ao gerar QR Code PIX:', err);
        setError('Erro ao gerar QR Code PIX');
        setQrCodeDataURL(null);
      } finally {
        setIsLoading(false);
      }
    };

    generateQRCode();
  }, [pixData, grandTotal, size]);

  if (!pixData || !validatePixData(pixData)) {
    return null;
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ width: size, height: size }}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-red-50 border border-red-200 rounded ${className}`} style={{ width: size, height: size }}>
        <p className="text-red-600 text-sm text-center px-2">{error}</p>
      </div>
    );
  }

  if (!qrCodeDataURL) {
    return null;
  }

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <img 
        src={qrCodeDataURL} 
        alt="QR Code PIX" 
        width={size} 
        height={size}
        className="rounded"
      />
      <p className="text-xs text-gray-600 mt-1 text-center">
        PIX QR Code
      </p>
    </div>
  );
};

export default PixQRCode;