import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Truck, Package, MapPin } from 'lucide-react';

const ShippingTransportTab = ({ shippingData, setShippingData }) => {
  const handleShippingChange = (section, field, value) => {
    setShippingData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Shipping & Transport Details</h2>
        <p className="text-gray-600 mt-1">Configure shipping addresses and transport information</p>
      </div>

      <div className="flex items-center space-x-2 mb-6">
        <Checkbox id="addShippingDetails" defaultChecked />
        <Label htmlFor="addShippingDetails" className="text-sm font-medium">
          Add Shipping Details
        </Label>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Shipped From */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg">
              <Package className="w-5 h-5 mr-2 text-green-600" />
              Shipped From
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="warehouse">Select Warehouse</Label>
              <Select 
                value={shippingData.from.warehouse} 
                onValueChange={(value) => handleShippingChange('from', 'warehouse', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Warehouse" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="main-warehouse">Main Warehouse</SelectItem>
                  <SelectItem value="secondary-warehouse">Secondary Warehouse</SelectItem>
                  <SelectItem value="branch-office">Branch Office</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromBusinessName">Business / Franchise Name</Label>
              <Input
                id="fromBusinessName"
                value={shippingData.from.businessName}
                onChange={(e) => handleShippingChange('from', 'businessName', e.target.value)}
                placeholder="Same as client address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fromCountry">Select Country</Label>
                <Select 
                  value={shippingData.from.country} 
                  onValueChange={(value) => handleShippingChange('from', 'country', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select Country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Brazil">Brazil</SelectItem>
                    <SelectItem value="USA">United States</SelectItem>
                    <SelectItem value="UK">United Kingdom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="fromCity">Address (optional)</Label>
                <Input
                  id="fromCity"
                  value={shippingData.from.address}
                  onChange={(e) => handleShippingChange('from', 'address', e.target.value)}
                  placeholder="Address"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fromCityName">City (optional)</Label>
                <Input
                  id="fromCityName"
                  value={shippingData.from.city}
                  onChange={(e) => handleShippingChange('from', 'city', e.target.value)}
                  placeholder="City"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fromPostalCode">Postal Code / ZIP Code</Label>
                <Input
                  id="fromPostalCode"
                  value={shippingData.from.postalCode}
                  onChange={(e) => handleShippingChange('from', 'postalCode', e.target.value)}
                  placeholder="Postal Code"
                />
              </div>
            </div>

            <Button variant="ghost" size="sm" className="text-green-600">
              <Plus className="w-4 h-4 mr-2" />
              Add More Fields
            </Button>
          </CardContent>
        </Card>

        {/* Shipped To */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg">
              <MapPin className="w-5 h-5 mr-2 text-blue-600" />
              Shipped To
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="toAddress">Select a Shipping Address</Label>
              <Select 
                value={shippingData.to.address} 
                onValueChange={(value) => handleShippingChange('to', 'address', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a Shipping Address" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="same-as-client">Same as client's address</SelectItem>
                  <SelectItem value="different-address">Different address</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="toBusinessName">Client's business name</Label>
              <Input
                id="toBusinessName"
                value={shippingData.to.businessName}
                onChange={(e) => handleShippingChange('to', 'businessName', e.target.value)}
                placeholder="Client's business name"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="toCountry">Select Country</Label>
                <Select 
                  value={shippingData.to.country} 
                  onValueChange={(value) => handleShippingChange('to', 'country', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select Country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Brazil">Brazil</SelectItem>
                    <SelectItem value="USA">United States</SelectItem>
                    <SelectItem value="UK">United Kingdom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="toCity">Address (optional)</Label>
                <Input
                  id="toCity"
                  value={shippingData.to.address}
                  onChange={(e) => handleShippingChange('to', 'address', e.target.value)}
                  placeholder="Address"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="toCityName">City (optional)</Label>
                <Input
                  id="toCityName"
                  value={shippingData.to.city}
                  onChange={(e) => handleShippingChange('to', 'city', e.target.value)}
                  placeholder="City"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="toPostalCode">Postal Code / ZIP Code</Label>
                <Input
                  id="toPostalCode"
                  value={shippingData.to.postalCode}
                  onChange={(e) => handleShippingChange('to', 'postalCode', e.target.value)}
                  placeholder="Postal Code"
                />
              </div>
            </div>

            <div className="pt-2">
              <Button variant="ghost" size="sm" className="text-blue-600">
                Save to client details
              </Button>
            </div>

            <Button variant="ghost" size="sm" className="text-blue-600">
              <Plus className="w-4 h-4 mr-2" />
              Add More Fields
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Transport Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Truck className="w-5 h-5 mr-2 text-orange-600" />
            Transport Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="transporterDetails">Transporter Details</Label>
            <Select 
              value={shippingData.transport.details} 
              onValueChange={(value) => handleShippingChange('transport', 'details', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Transporter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fedex">FedEx</SelectItem>
                <SelectItem value="dhl">DHL</SelectItem>
                <SelectItem value="ups">UPS</SelectItem>
                <SelectItem value="correios">Correios</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="distance">Distance</Label>
            <Input
              id="distance"
              value={shippingData.transport.distance}
              onChange={(e) => handleShippingChange('transport', 'distance', e.target.value)}
              placeholder="Distance"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="text-orange-600">
                <Plus className="w-4 h-4 mr-2" />
                Add Mode of Transport
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="text-orange-600">
                <Plus className="w-4 h-4 mr-2" />
                Add Transport Doc No.
              </Button>
            </div>
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="text-orange-600">
                <Plus className="w-4 h-4 mr-2" />
                Add Transport Doc Date
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="text-orange-600">
                <Plus className="w-4 h-4 mr-2" />
                Vehicle Type
              </Button>
            </div>
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="text-orange-600">
                <Plus className="w-4 h-4 mr-2" />
                Vehicle Number
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ShippingTransportTab;