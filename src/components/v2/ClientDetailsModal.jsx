import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { X, Plus, ChevronDown, User } from 'lucide-react';

const ClientDetailsModal = ({ open, onOpenChange, clientData, setClientData }) => {
  const [customFields, setCustomFields] = useState([]);

  const handleInputChange = (field, value) => {
    setClientData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    onOpenChange(false);
  };

  const addCustomField = () => {
    setCustomFields(prev => [...prev, { label: '', value: '' }]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">Add New Client</DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Client Avatar */}
          <div className="flex items-center justify-center">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center">
              <User className="w-10 h-10 text-gray-400" />
              <Button
                variant="ghost"
                size="icon"
                className="absolute -mt-2 -ml-2 w-6 h-6 bg-gray-200 rounded-full"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* Basic Information */}
          <Accordion type="single" defaultValue="basic-info" className="w-full">
            <AccordionItem value="basic-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Basic Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessName">Business Name*</Label>
                      <Input
                        id="businessName"
                        value={clientData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Contabilizei Contabilidade"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="clientIndustry">Client Industry</Label>
                      <Select value={clientData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Legal Services" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Legal Services">Legal Services</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Healthcare">Healthcare</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="country">Select Country*</Label>
                      <Select value={clientData.country} onValueChange={(value) => handleInputChange('country', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Brazil" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Brazil">Brazil</SelectItem>
                          <SelectItem value="USA">United States</SelectItem>
                          <SelectItem value="UK">United Kingdom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="city">City/Town</Label>
                      <Input
                        id="city"
                        value={clientData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="São Paulo"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Tax Information */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="tax-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Tax Information</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="vatNumber">VAT Number</Label>
                    <Input
                      id="vatNumber"
                      value={clientData.vatNumber}
                      onChange={(e) => handleInputChange('vatNumber', e.target.value)}
                      placeholder="VAT Number"
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Address */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="address" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Address</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="addressCountry">Select Country</Label>
                      <Select value={clientData.country} onValueChange={(value) => handleInputChange('country', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Brazil" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Brazil">Brazil</SelectItem>
                          <SelectItem value="USA">United States</SelectItem>
                          <SelectItem value="UK">United Kingdom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State / Province</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select State / Province" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sp">São Paulo</SelectItem>
                          <SelectItem value="rj">Rio de Janeiro</SelectItem>
                          <SelectItem value="mg">Minas Gerais</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="addressCity">City/Town</Label>
                      <Input
                        id="addressCity"
                        value={clientData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="São Paulo"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="postalCode">Postal Code / Zip Code</Label>
                      <Input
                        id="postalCode"
                        value={clientData.postalCode}
                        onChange={(e) => handleInputChange('postalCode', e.target.value)}
                        placeholder="Postal Code / Zip Code"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="streetAddress">Street Address</Label>
                    <Input
                      id="streetAddress"
                      value={clientData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="Street Address"
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Additional Details */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="additional" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Additional Details</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessAlias">Business Alias (Nick Name)</Label>
                      <Input
                        id="businessAlias"
                        value={clientData.alias}
                        onChange={(e) => handleInputChange('alias', e.target.value)}
                        placeholder="Business Alias"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="uniqueKey">Unique Key</Label>
                      <Input
                        id="uniqueKey"
                        value={clientData.uniqueKey}
                        onChange={(e) => handleInputChange('uniqueKey', e.target.value)}
                        placeholder="Unique Identification Key"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <p className="text-sm text-gray-500">Add to directly email documents from Refrens</p>
                      <Input
                        id="email"
                        type="email"
                        value={clientData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Email"
                      />
                      <div className="flex items-center space-x-2">
                        <Checkbox id="showEmailInInvoice" />
                        <Label htmlFor="showEmailInInvoice" className="text-sm">Show Email in Invoice</Label>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone No.</Label>
                      <p className="text-sm text-gray-500">Add to directly WhatsApp documents from Refrens</p>
                      <div className="flex">
                        <Select defaultValue="+55">
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="+55">🇧🇷 +55</SelectItem>
                            <SelectItem value="+1">🇺🇸 +1</SelectItem>
                            <SelectItem value="+44">🇬🇧 +44</SelectItem>
                          </SelectContent>
                        </Select>
                        <Input
                          className="rounded-l-none"
                          value={clientData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+55"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="showPhoneInInvoice" />
                        <Label htmlFor="showPhoneInInvoice" className="text-sm">Show Phone in Invoice</Label>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600"
                    onClick={addCustomField}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Custom Fields
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Account Details */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="account" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Account Details</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-sm">
                    <span>Enable Advanced Accounting to create or link ledger.</span>
                    <Button variant="link" className="text-blue-600 p-0 h-auto">
                      Enable Now
                    </Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        <div className="flex justify-end pt-6 border-t">
          <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ClientDetailsModal;