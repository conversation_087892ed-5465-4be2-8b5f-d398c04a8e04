import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Download, 
  Mail, 
  MessageSquare, 
  MoreHorizontal,
  Edit,
  Upload,
  Plus,
  ChevronDown,
  Signature,
  FileText,
  Settings,
  CreditCard,
  Eye,
  History,
  BarChart3
} from 'lucide-react';
import { formatCurrency } from '../../utils/formatCurrency';
import { templates, getTemplate } from '../../utils/templateRegistry';

const PreviewSummaryTab = ({ 
  invoiceData,
  businessData,
  clientData,
  items,
  taxPercentage,
  selectedCurrency,
  selectedTemplate = 1,
  notes,
  setNotes,
  termsConditions,
  setTermsConditions,
  signature,
  setSignature,
  pixData,
  setPixData,
  onTemplateSelect,
  onFullPreview
}) => {
  const calculateSubTotal = () => {
    return items.reduce((sum, item) => sum + (item.amount || 0), 0);
  };

  const calculateTaxAmount = () => {
    const subTotal = calculateSubTotal();
    return (subTotal * taxPercentage) / 100;
  };

  const calculateGrandTotal = () => {
    return calculateSubTotal() + calculateTaxAmount();
  };

  const handleTermChange = (index, value) => {
    const newTerms = [...termsConditions];
    newTerms[index] = { ...newTerms[index], text: value };
    setTermsConditions(newTerms);
  };

  const addNewTerm = () => {
    setTermsConditions([...termsConditions, { text: "" }]);
  };

  const removeTerm = (index) => {
    if (termsConditions.length > 1) {
      setTermsConditions(termsConditions.filter((_, i) => i !== index));
    }
  };

  // Prepare data for template rendering
  const templateData = {
    billTo: { 
      name: clientData.name, 
      address: clientData.address, 
      phone: clientData.phone,
      email: clientData.email
    },
    shipTo: { 
      name: clientData.name, 
      address: clientData.address, 
      phone: clientData.phone 
    },
    invoice: { 
      date: invoiceData.date, 
      paymentDate: invoiceData.paymentDate, 
      number: invoiceData.number 
    },
    yourCompany: { 
      name: businessData.name, 
      address: businessData.address, 
      phone: businessData.phone,
      email: businessData.email,
      vatNumber: businessData.vatNumber
    },
    items: items.map(item => ({ 
      ...item, 
      amount: item.rate, 
      total: item.amount 
    })),
    taxPercentage,
    taxAmount: calculateTaxAmount(),
    subTotal: calculateSubTotal(),
    grandTotal: calculateGrandTotal(),
    notes,
    selectedCurrency,
    pixData,
    signature,
    termsConditions,
  };

  // Get the selected template component
  const SelectedTemplateComponent = getTemplate(selectedTemplate);

  return (
    <div className="space-y-8">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Invoice Summary</h2>
          <p className="text-gray-600 mt-1">Review your invoice and customize the final details</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="w-4 h-4 mr-2" />
            Record Payment
          </Button>
          <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
          <div className="flex">
            <Button variant="outline" size="sm" className="rounded-r-none">
              <Mail className="w-4 h-4 mr-2" />
              Email
            </Button>
            <Button variant="outline" size="sm" className="rounded-l-none border-l-0">
              <MessageSquare className="w-4 h-4 mr-2" />
              WhatsApp
            </Button>
          </div>
          <Button variant="outline" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Invoice Preview */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between">
                <span>Invoice Preview</span>
                <Button variant="ghost" size="sm" onClick={onFullPreview}>
                  <Eye className="w-4 h-4 mr-2" />
                  Full Preview
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Actual Template Preview */}
              <div className="mb-6 border rounded-lg overflow-hidden bg-white">
                <div style={{ transform: 'scale(0.6)', transformOrigin: 'top left', width: '166.67%', height: '166.67%' }}>
                  <SelectedTemplateComponent data={templateData} />
                </div>
              </div>

              {/* Template Selection */}
              <div>
                <h4 className="font-semibold mb-4">Choose Template</h4>
                <div className="grid grid-cols-3 gap-4">
                  {templates.slice(0, 6).map((template, index) => (
                    <div
                      key={index}
                      className={`border-2 rounded-lg p-3 cursor-pointer hover:border-purple-500 transition-colors ${
                        selectedTemplate === index + 1 
                          ? 'border-purple-500 ring-2 ring-purple-200' 
                          : 'border-gray-200'
                      }`}
                      onClick={() => onTemplateSelect(index + 1)}
                    >
                      <div className="aspect-[3/4] bg-gray-100 rounded mb-2 overflow-hidden">
                        <img
                          src={`/assets/template-${index + 1}.jpeg`}
                          alt={`Template ${index + 1} preview`}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                        <div className="w-full h-full hidden items-center justify-center">
                          <span className="text-xs text-gray-500">Template {index + 1}</span>
                        </div>
                      </div>
                      <p className={`text-xs text-center font-medium ${
                        selectedTemplate === index + 1 ? 'text-purple-600' : 'text-gray-700'
                      }`}>
                        {template.name}
                        {selectedTemplate === index + 1 && (
                          <span className="block text-xs text-purple-500 mt-1">✓ Selected</span>
                        )}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Invoice Summary Collapsible */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-base">
                <div className="flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Invoice Summary
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
          </Card>

          {/* Signature */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <Signature className="w-4 h-4 mr-2" />
                Signature
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                {signature ? (
                  <img src={signature} alt="Signature" className="max-w-full h-auto" />
                ) : (
                  <div>
                    <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500">Upload signature</p>
                  </div>
                )}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Signature
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  Use Signature Pad
                </Button>
              </div>
              <div>
                <Label htmlFor="signatureLabel" className="text-sm font-medium">Add signature label</Label>
                <Input
                  id="signatureLabel"
                  placeholder="Authorised Signatory"
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <Plus className="w-4 h-4 mr-2" />
                Add Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Notas adicionais: Lorem ipsum dolor sit amet"
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <FileText className="w-4 h-4 mr-2" />
                Add Terms & Conditions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {termsConditions.map((term, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <span className="text-sm font-medium mt-2">{String(index + 1).padStart(2, '0')}.</span>
                    <Textarea
                      value={term.text}
                      onChange={(e) => handleTermChange(index, e.target.value)}
                      placeholder="Enter term or condition"
                      rows={2}
                      className="flex-1"
                    />
                    {termsConditions.length > 1 && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeTerm(index)}
                        className="text-red-500 mt-1"
                      >
                        <Plus className="w-4 h-4 rotate-45" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={addNewTerm}
                  className="text-blue-600"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Term
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-blue-600"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Group
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <Plus className="w-4 h-4 mr-2" />
                Add Additional Info
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Input
                  placeholder="PIX"
                  value="<EMAIL>"
                  readOnly
                  className="bg-gray-50"
                />
                <Button variant="ghost" size="sm" className="text-blue-600">
                  <Plus className="w-4 h-4 mr-2" />
                  Add More Fields
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-base">
                <span>Your Business Profile is Ready</span>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
          </Card>

          {/* Additional Cards */}
          <div className="space-y-2">
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Settings className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Customize Invoice Design</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CreditCard className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Bank Details</span>
                  <span className="text-xs bg-gray-200 px-2 py-1 rounded ml-2">Not enabled</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Settings className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Online Payment Options</span>
                  <span className="text-xs bg-gray-200 px-2 py-1 rounded ml-2">Not enabled</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">View Journal</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <History className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Acceptance History</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <BarChart3 className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Audit Trail</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewSummaryTab;