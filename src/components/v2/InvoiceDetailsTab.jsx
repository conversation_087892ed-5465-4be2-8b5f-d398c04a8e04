import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Upload, Calendar, Plus } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";

const InvoiceDetailsTab = ({ invoiceData, setInvoiceData }) => {
  const handleInputChange = (field, value) => {
    setInvoiceData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        handleInputChange('logoUrl', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Invoice Details</h2>
          <p className="text-gray-600 mt-1">Configure your invoice basic information</p>
        </div>
        <div className="text-right">
          <div className="flex items-center space-x-4">
            <div>
              <Button variant="outline" size="sm" className="relative">
                <Upload className="w-4 h-4 mr-2" />
                Add Business Logo
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </Button>
              <p className="text-xs text-gray-500 mt-1">PNG or JPEG file</p>
            </div>
            {invoiceData.logoUrl && (
              <div className="w-16 h-16 border rounded-lg overflow-hidden">
                <img 
                  src={invoiceData.logoUrl} 
                  alt="Business Logo" 
                  className="w-full h-full object-cover"
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Invoice Information */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="invoiceNumber" className="text-sm font-medium">
                Invoice No*
              </Label>
              <Input
                id="invoiceNumber"
                value={invoiceData.number}
                onChange={(e) => handleInputChange('number', e.target.value)}
                placeholder="A00001"
                className="font-mono"
              />
              <p className="text-xs text-gray-500">
                Last No: A00001 (Aug 19, 2025)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="invoiceDate" className="text-sm font-medium">
                Invoice Date*
              </Label>
              <div className="relative">
                <Input
                  id="invoiceDate"
                  type="date"
                  value={invoiceData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  className="pl-10"
                />
                <Calendar className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dueDate" className="text-sm font-medium">
                Due Date
              </Label>
              <div className="relative">
                <Input
                  id="dueDate"
                  type="date"
                  value={invoiceData.paymentDate}
                  onChange={(e) => handleInputChange('paymentDate', e.target.value)}
                  className="pl-10"
                />
                <Calendar className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Button variant="ghost" size="sm" className="text-purple-600">
              <Plus className="w-4 h-4 mr-2" />
              Add More Fields
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
            <span className="text-white text-xs font-bold">i</span>
          </div>
          <div>
            <h4 className="font-medium text-blue-900">Pro Tip</h4>
            <p className="text-sm text-blue-700 mt-1">
              Use consistent invoice numbering to maintain professional records. The system automatically suggests the next number based on your last invoice.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDetailsTab;